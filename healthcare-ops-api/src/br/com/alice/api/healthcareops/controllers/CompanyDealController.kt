package br.com.alice.api.healthcareops.controllers

import br.com.alice.api.healthcareops.models.CancelCompanyDealRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.DealStage
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import com.github.kittinunf.result.flatMap
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.*

class CompanyDealController(
    private val ongoingCompanyDealService: OngoingCompanyDealService
): Controller() {
    suspend fun cancelCompanyDeal(id: UUID, request: CancelCompanyDealRequest): Response {
        return ongoingCompanyDealService.get(id).flatMap { deal ->
            if (deal.status == DealStage.CANCELED) {
                throw InvalidArgumentException(
                    code = "deal_already_canceled",
                    message = "The deal was already canceled"
                )
            }

            ongoingCompanyDealService.cancel(deal, request.reason, request.description)
                .then { logger.info("companyDeal canceled", "deal_id" to id) }
                .thenError { logger.error("companyDeal cancel failed", "ex" to it, "request" to request) }
        }.foldResponse()
    }

    suspend fun reactivateCompanyDeal(id: UUID): Response {
        return ongoingCompanyDealService.get(id).flatMap { deal ->
            if (deal.status != DealStage.CANCELED) {
                throw InvalidArgumentException("already_active_deal", "The deal is already active")
            }

            if (ChronoUnit.DAYS.between(deal.updatedAt, LocalDateTime.now()) > 30) {
                throw InvalidArgumentException("deal_expired", "The deal was cancelled more than 30 days ago")
            }

            if (deal.statusHistory.isEmpty()) {
                logger.info(
                    "CompanyDealController::reactivateCompanyDeal",
                    "exception" to "DealHasNoPreviousStatusException",
                    "deal_id" to deal.id,
                    "status_history" to deal.statusHistory
                )
                throw InvalidArgumentException("deal_has_no_previous_status", "Deal has no previous status")
            }
            ongoingCompanyDealService.reactivate(deal)
                .then { logger.info("companyDeal reactivated", "deal_id" to id) }
                .thenError { logger.error("companyDeal reactivate failed", "ex" to it, "deal_id" to id) }
        }.foldResponse()
    }
}
