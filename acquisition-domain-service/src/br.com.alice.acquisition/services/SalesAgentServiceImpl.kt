package br.com.alice.acquisition.services

import br.com.alice.acquisition.client.SalesAgentService
import br.com.alice.acquisition.converters.AssociationBuilder
import br.com.alice.acquisition.converters.ContactConverter.toContact
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.SalesAgent
import br.com.alice.hubspot.integration.lib.clients.HubspotClient
import br.com.alice.hubspot.integration.lib.models.ContactUpdateProperties
import com.github.kittinunf.result.Result

class SalesAgentServiceImpl(
    private val hubspotClient: HubspotClient,
): SalesAgentService {
    override suspend fun createSalesAgentAndAssociateToPotentialCustomer(
        salesAgent: SalesAgent,
        externalOfferId: String,
        externalCommercialRepresentativeId: String,
    ): Result<Boolean, Throwable> {
        val salesAgentContact = salesAgent.toContact()
        val salesAgentFromHubspot = hubspotClient.getContactByEmail(salesAgentContact.email)

        logger.info(
            "Creating sales agent and associating to potential customer",
            "sales_agent" to salesAgent,
            "external_offer_id" to externalOfferId,
            "external_commercial_representative_id" to externalCommercialRepresentativeId,
        )

        val salesAgentContactId = if (salesAgentFromHubspot != null) {
            logger.info(
                "Sales agent already exists on Hubspot",
                "sales_agent" to salesAgent,
                "sales_agent_id" to salesAgentFromHubspot.id,
            )

            salesAgentFromHubspot.id
        } else {
            logger.info(
                "Creating sales agent on Hubspot",
                "sales_agent" to salesAgent,
            )

            hubspotClient.createContact(salesAgentContact).id
        }

        if (salesAgentContact.phone != null) {
            hubspotClient.updateContact(salesAgentContactId, ContactUpdateProperties(salesAgentContact.phone!!))
        }

        val associations = AssociationBuilder.buildSalesAgentAndDecisionMakerAssociation(
            salesAgentContactId = salesAgentContactId,
            dealId = externalOfferId,
            contactId = externalCommercialRepresentativeId,
        )

        hubspotClient.associateContactToDeal(associations)

        logger.info(
            "Sales agent created and associated to potential customer",
            "sales_agent" to salesAgent,
            "sales_agent_id" to salesAgentContactId,
            "external_offer_id" to externalOfferId,
            "external_commercial_representative_id" to externalCommercialRepresentativeId,
        )

        return Result.success(true)
    }
}
