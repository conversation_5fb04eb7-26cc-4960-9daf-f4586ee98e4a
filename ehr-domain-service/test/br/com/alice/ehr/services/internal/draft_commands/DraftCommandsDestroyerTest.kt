package br.com.alice.ehr.services.internal.draft_commands

import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.DraftCommand
import br.com.alice.data.layer.models.DraftCommandAction
import br.com.alice.data.layer.models.DraftCommandReferencedModel
import br.com.alice.data.layer.models.DraftCommandStatus
import br.com.alice.data.layer.services.DraftCommandModelDataService
import br.com.alice.ehr.converters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class DraftCommandsDestroyerTest {
    private val dataService: DraftCommandModelDataService = mockk()

    @Test
    fun `#call sets all appointment pending commands as discarded`() = runBlocking {
        val draftAppointment = TestModelFactory.buildAppointment().copy(
            status = AppointmentStatus.DRAFT,
        )
        val addCommand = DraftCommand(
            appointmentId = draftAppointment.id,
            action = DraftCommandAction.ADD,
            serializedModel = "",
            referencedModel = DraftCommandReferencedModel.PREGNANCY,
            status = DraftCommandStatus.PENDING,
        ).toModel()
        val updateCommand = DraftCommand(
            appointmentId = draftAppointment.id,
            action = DraftCommandAction.UPDATE,
            serializedModel = "",
            referencedModel = DraftCommandReferencedModel.PREGNANCY,
            status = DraftCommandStatus.PENDING,
        ).toModel()

        coEvery {
            dataService.find(
                queryEq {
                    where {
                        this.appointmentId.eq(draftAppointment.id) and
                            this.status.eq(DraftCommandStatus.PENDING.toString())
                    }
                }
            )
        } returns listOf(addCommand, updateCommand).success()

        coEvery {
            dataService.update(any())
        } returns addCommand.success()

        val subject = DraftCommandsDestroyer(dataService, draftAppointment.id)

        subject.call()

        coVerify(exactly = 1) { dataService.find(any()) }

        coVerify(exactly = 2) { dataService.update(any()) }
        coVerify(exactly = 1) {
            dataService.update(
                addCommand.copy(status = DraftCommandStatus.DISCARDED)
            )
        }
        coVerify(exactly = 1) {
            dataService.update(
                updateCommand.copy(status = DraftCommandStatus.DISCARDED)
            )
        }
    }
}
