package br.com.alice.schedule.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Status
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.AgeRatingType
import br.com.alice.data.layer.models.AppointmentSchedule.Companion.testTypes
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeMemberRisk
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.AppointmentScheduleType.HEALTHCARE_TEAM
import br.com.alice.data.layer.models.AppointmentScheduleType.IMMERSION
import br.com.alice.data.layer.models.AppointmentScheduleType.TEST
import br.com.alice.data.layer.models.CalendarProviderUnit
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.HealthcareTeam.Type.LEAGUE
import br.com.alice.data.layer.models.HealthcareTeam.Type.LEAN
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.data.layer.services.AppointmentScheduleOptionModelDataService
import br.com.alice.data.layer.services.AppointmentScheduleOptionModelDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentScheduleOptionModelDataService.OrderingOptions
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionFilters
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.converters.AppointmentScheduleEventTypeWithProviderUnitsConverter
import br.com.alice.schedule.converters.AppointmentScheduleOptionCreator
import br.com.alice.schedule.converters.AppointmentScheduleOptionCreator.filterUnitsNominalOptions
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.converters.toTransport
import br.com.alice.schedule.model.AppointmentScheduleEventTypeWithProviderUnits
import br.com.alice.schedule.model.EventTypeProviderUnits
import br.com.alice.schedule.model.events.AppointmentScheduleOptionCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleOptionUpdatedEvent
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class AppointmentScheduleOptionServiceImpl(
    private val appointmentScheduleOptionModelDataService: AppointmentScheduleOptionModelDataService,
    private val healthcareTeamService: HealthcareTeamService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val riskService: RiskService,
    private val eventTypeProviderUnitService: EventTypeProviderUnitServiceImpl,
    private val staffService: StaffService,
    private val staffScheduleService: StaffScheduleServiceImpl,
    private val personService: PersonService,
    private val cache: GenericCache,
    private val kafkaProducerService: KafkaProducerService,
    private val providerUnitService: ProviderUnitService,
) : AppointmentScheduleOptionService {

    // visible on app for league: only healthcare_team and test
    private val typesOfOptionsThatAreVisibleToLeagueMembers = listOf(TEST, HEALTHCARE_TEAM)

    private val Person.isChildrenAppointmentSchedulesEligible: Boolean get() = age < 12

    override suspend fun listByPerson(
        personId: PersonId,
        fromReferral: Boolean,
        subSpecialtyIds: List<UUID>,
    ): Result<List<AppointmentScheduleOption>, Throwable> = span("listByPerson") { span ->
        coResultOf<List<AppointmentScheduleOption>, Throwable> {
            span.setAttribute("person_id", personId)
            span.setAttribute("from_referral", fromReferral)
            span.setAttribute("sub_specialty_ids_size", subSpecialtyIds.size)

            coroutineScope {
                val personDeferred = async { personService.get(personId).get() }
                val healthcareTeamDeferred =
                    async { healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNullIfNotFound() }

                val appointmentScheduleOptionsResultDeferred = async {
                    val healthcareModelType =
                        personDeferred.await().productInfo?.healthcareModelType ?: HealthcareModelType.V3
                    span.setAttribute("healthcare_model_type", healthcareModelType)
                    if (!fromReferral) getOptionsVisibleOnApp(healthcareTeamDeferred.await(), healthcareModelType)
                    else getOptionsForReferral(subSpecialtyIds, healthcareModelType)
                }

                val appointmentScheduleOptions = appointmentScheduleOptionsResultDeferred.await().get()
                span.setAttribute("appointment_schedule_options_size", appointmentScheduleOptions.size)

                val appointmentScheduleOptionsWithoutAgeFilter =
                    appointmentScheduleOptions.listByHealthcareTeamAndCheckFromReferral(
                        healthcareTeam = healthcareTeamDeferred.await(),
                        fromReferral = fromReferral
                    )

                val person = personDeferred.await()
                span.setAttribute("is_children_appointment_schedules_eligible", person.isChildrenAppointmentSchedulesEligible)
                span.setAttribute("is_new_portfolio", person.isNewPortfolio)

                val appointmentScheduleOptionsByPersonAge =
                    appointmentScheduleOptionsWithoutAgeFilter.filterOptionsByPersonAge(
                        person.isChildrenAppointmentSchedulesEligible
                    )

                val appointmentScheduleEventTypes =
                    appointmentScheduleOptionsByPersonAge.listAppointmentScheduleEventTypes()

                val eventTypeProviderUnitsMap =
                    appointmentScheduleEventTypes.listEventTypeProviderUnits()
                        .associateBy { it.appointmentScheduleEventTypeId }

                val optionsFiltered = if (fromReferral) appointmentScheduleOptionsByPersonAge
                else {
                    filterBasedOnTeamModelAndRisk(
                        options = appointmentScheduleOptionsByPersonAge,
                        team = healthcareTeamDeferred.await(),
                        risk = riskService.getByPerson(personId).getOrNullIfNotFound(),
                        isNewPortfolio = person.isNewPortfolio,
                        appointmentScheduleEventTypes = appointmentScheduleEventTypes
                    )
                }
                val staffSchedules =
                    optionsFiltered.mapNotNull { it.staffId }.distinct().takeIf { it.isNotEmpty() }?.let { staffIds ->
                        staffScheduleService.getActiveByStaffs(staffIds)
                            .get()
                            .groupBy { it.staffId }
                    } ?: emptyMap()


                val appointmentScheduleOptionsNotAssociatedToEventTypes = optionsFiltered.filter {
                    it.appointmentScheduleEventTypeId == null
                }
                val appointmentScheduleOptionsAssociatedToEventTypes =
                    filterAppointmentScheduleOptionsAssociatedToEventTypes(
                        optionsFiltered,
                        appointmentScheduleEventTypes.associateBy { it.id },
                        fromReferral,
                        eventTypeProviderUnitsMap,
                        healthcareTeamDeferred.await(),
                        staffSchedules
                    )

                appointmentScheduleOptionsNotAssociatedToEventTypes + appointmentScheduleOptionsAssociatedToEventTypes
            }
        }.recordResult(span)
    }

    private suspend fun getOptionsForReferral(
        subSpecialtyIds: List<UUID>,
        healthcareModelType: HealthcareModelType
    ): Result<List<AppointmentScheduleOption>, Throwable> = coResultOf {
        coroutineScope {
            // Tests are not related with sub specialty ids, we add those to the sub specialty ids that come
            // from member referrals
            val testOptionsDeferred = async {
                getActiveAndVisibleTestOptions()
            }
            val activeOptionsForSubSpecialtiesDeferred = async {
                findActiveForSubSpecialties(subSpecialtyIds, healthcareModelType).get()
            }
            awaitAll(testOptionsDeferred, activeOptionsForSubSpecialtiesDeferred).flatten().distinct()
        }
    }

    private suspend fun getActiveAndVisibleTestOptions() =
        findActiveWithTypes(testTypes()).get()

    private suspend fun getOptionsVisibleOnApp(
        healthcareTeam: HealthcareTeam?,
        healthcareModelType: HealthcareModelType
    ): Result.Success<List<AppointmentScheduleOption>> {
        val visibleOnAppOptions = if (
            healthcareTeam?.isLeague() == true
            || healthcareTeam?.isLean() == true
        ) {
            findActiveAndVisibleOnAppWithTypes(typesOfOptionsThatAreVisibleToLeagueMembers, healthcareModelType).get()
        } else {
            findActiveAndVisibleOnAppAndModelType(healthcareModelType).get()
        }

        val testOptions = getActiveAndVisibleTestOptions()

        return (visibleOnAppOptions + testOptions).success()
    }

    override suspend fun listByPersonAndType(
        personId: PersonId,
        type: AppointmentScheduleType,
        fromReferral: Boolean,
        subSpecialtyIds: List<UUID>,
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        listByPerson(personId, fromReferral, subSpecialtyIds)
            .map { options ->
                options.filter { it.type == type }
            }

    override suspend fun searchScheduleOption(
        titlePrefix: String,
        range: IntRange
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                title.like(titlePrefix) and
                        this.active.eq(true)
            }
                .orderBy { title }
                .sortOrder { asc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun getForSpecialist(specialistId: UUID): Result<AppointmentScheduleOption, Throwable> =
        appointmentScheduleOptionModelDataService.findOne {
            where { this.healthCommunitySpecialistId.eq(specialistId) }
        }.map { it.toTransport() }

    override suspend fun getForSpecialists(specialistIds: List<UUID>): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                this.healthCommunitySpecialistId.inList(specialistIds).and(this.active.eq(true))
            }
        }.mapEach { it.toTransport() }

    override suspend fun getEventsWithProviderUnits(
        staffId: UUID
    ): Result<List<AppointmentScheduleEventTypeWithProviderUnits>, Throwable> =
        getForStaff(staffId)
            .map { options -> options.mapNotNull { it.appointmentScheduleEventTypeId } }
            .flatMap { eventTypeIds -> appointmentScheduleEventTypeService.getActiveByIds(eventTypeIds) }
            .flatMapPair { eventTypes -> eventTypeProviderUnitService.getForEventTypes(eventTypes.map { it.id }) }
            .map { (eventTypeProviderUnits, eventTypes) ->

                val providerUnitMap = appointmentScheduleEventTypeService.getProviderUnitsName(eventTypeProviderUnits).get()

                eventTypes.map { eventType ->
                    AppointmentScheduleEventTypeWithProviderUnitsConverter.convert(
                        source = eventType,
                        eventTypeProviderUnits = eventTypeProviderUnits.filter { it.appointmentScheduleEventTypeId == eventType.id },
                        providerUnitMap
                    )
                }
            }

    override suspend fun associateStaff(
        staffId: UUID,
        appointmentScheduleEventTypeId: UUID
    ): Result<AppointmentScheduleOption, Throwable> =
        span("associateStaff") { span ->
            getByAppointmentScheduleEventTypeAndStaff(appointmentScheduleEventTypeId, staffId)
                .flatMap { updateOldAssociate(it, appointmentScheduleEventTypeId, staffId) }
                .coFoldNotFound { createNewAssociate(appointmentScheduleEventTypeId, staffId) }
                .recordResult(span)
        }

    override suspend fun disassociateStaff(
        staffId: UUID,
        appointmentScheduleEventTypeId: UUID
    ): Result<AppointmentScheduleOption, Throwable> =
        span("disassociateStaff") { span ->
            getByAppointmentScheduleEventTypeAndStaff(appointmentScheduleEventTypeId, staffId)
                .flatMap { update(it.copy(active = false)) }
                .recordResult(span)
        }

    override suspend fun get(id: UUID): Result<AppointmentScheduleOption, Throwable> =
        appointmentScheduleOptionModelDataService.get(id).map { it.toTransport() }

    override suspend fun add(model: AppointmentScheduleOption): Result<AppointmentScheduleOption, Throwable> =
        span("add") { span ->
            appointmentScheduleOptionModelDataService.add(model.toModel())
                .map { it.toTransport() }
                .then { kafkaProducerService.produce(AppointmentScheduleOptionCreatedEvent(it)) }
                .recordResult(span)
        }

    override suspend fun update(model: AppointmentScheduleOption): Result<AppointmentScheduleOption, Throwable> =
        span("update") { span ->
            appointmentScheduleOptionModelDataService.update(model.toModel())
                .map { it.toTransport() }
                .then { kafkaProducerService.produce(AppointmentScheduleOptionUpdatedEvent(it)) }
                .recordResult(span)
        }

    override suspend fun findBy(filters: AppointmentScheduleOptionFilters): Result<List<AppointmentScheduleOption>, Throwable> =
        catchResult {
            filters.isValid()

            appointmentScheduleOptionModelDataService.find { this.byFilter(filters) }
                .map { it.map { it.toTransport() } }
        }

    override suspend fun countBy(filters: AppointmentScheduleOptionFilters): Result<Int, Throwable> =
        catchResult {
            filters.isValid()

            appointmentScheduleOptionModelDataService.count { this.byFilter(filters) }
        }

    override suspend fun getStaffsAssociatedToEvents(
        appointmentScheduleEventTypeId: UUID,
        staffIds: List<UUID>?,
    ): Result<List<Staff>, Throwable> =
        getByAppointmentScheduleEventType(appointmentScheduleEventTypeId, staffIds)
            .map { appointmentScheduleOptions -> appointmentScheduleOptions.mapNotNull { it.staffId } }
            .flatMap { staffService.findByList(it) }
            .map { staffs -> staffs.filter { it.active }.sortedBy { it.fullName } }


    override suspend fun getProviderUnitDetailsByStaffSchedule(
        personId: PersonId,
        eventId: UUID,
        staffId: UUID
    ): Result<List<ProviderUnit>, Throwable> = span("getProviderUnitDetailsByHealthcareTeamOnSiteSchedule") { span ->
        span.setAttribute("person_id", personId)
        span.setAttribute("appointment_schedule_event_id", eventId)
        span.setAttribute("staff_id", staffId)

        getByAppointmentScheduleEventTypeAndStaff(eventId, staffId)
            .map { appointmentScheduleOption -> appointmentScheduleOption.toCalendarProviderUnits() }
            .flatMapPair { staffScheduleService.getActiveOnSiteByStaff(staffId = staffId) }
            .flatMap { (schedules, providersAvailable) ->
                span.setAttribute("providers_available_ids", providersAvailable.joinToString(","))
                schedules
                    .mapNotNull { it.providerUnitId }
                    .distinct()
                    .filter { providersAvailable.contains(it) }.let { providerUnitIds ->
                        span.setAttribute("provider_unit_ids_size", providerUnitIds)
                        span.setAttribute("provider_unit_ids", providerUnitIds.joinToString(","))

                        if (providerUnitIds.isEmpty()) emptyList<ProviderUnit>().success()
                        else providerUnitService.getByIds(providerUnitIds, withAddress = false)
                    }

            }.recordResult(span)
    }

    private fun AppointmentScheduleOption.toCalendarProviderUnits() =
        this.calendarProviderUnits
            ?.map { it.providerUnitId }.orEmpty()

    suspend fun getByAppointmentScheduleEventType(
        appointmentScheduleEventTypeId: UUID,
        staffIds: List<UUID>?,
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        staffIds?.let {
            appointmentScheduleOptionModelDataService.find {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId)
                        .and(this.active.eq(true))
                        .and(this.staffId.inList(it))
                }
            }.map { it.map { it.toTransport() } }
        } ?: run {
            appointmentScheduleOptionModelDataService.find {
                where {
                    this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId)
                        .and(this.active.eq(true))
                }
            }.map { it.map { it.toTransport() } }
        }

    suspend fun getAllByAppointmentScheduleEventType(
        appointmentScheduleEventTypeId: UUID,
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId)
            }
        }.map { it.map { it.toTransport() } }

    suspend fun getByAppointmentScheduleEventTypes(
        appointmentScheduleEventTypeIds: List<UUID>
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                this.appointmentScheduleEventTypeId.inList(appointmentScheduleEventTypeIds)
                    .and(this.active.eq(true))
            }
        }.map { it.map { it.toTransport() } }

    suspend fun getForStaff(staffId: UUID) =
        appointmentScheduleOptionModelDataService.find {
            where {
                this.staffId.eq(staffId).and(this.active.eq(true))
            }
        }.map { it.map { it.toTransport() } }

    suspend fun getByAppointmentScheduleEventTypeAndStaff(
        appointmentScheduleEventTypeId: UUID,
        staffId: UUID
    ): Result<AppointmentScheduleOption, Throwable> =
        appointmentScheduleOptionModelDataService.findOne {
            where {
                this.appointmentScheduleEventTypeId.eq(appointmentScheduleEventTypeId)
                    .and(this.staffId.eq(staffId))
                    .and(this.active.eq(true))
            }
        }.map { it.toTransport() }

    suspend fun getActiveForSpecialtyAndSubSpecialty(
        specialtyId: UUID,
        subSpecialtyId: UUID
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                this.specialtyId.eq(specialtyId)
                    .and(this.subSpecialtyIds.contains(subSpecialtyId))
                    .and(this.active.eq(true))
            }
        }.map { it.map { it.toTransport() } }

    suspend fun updateList(
        options: List<AppointmentScheduleOption>
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        span("updateList") { span ->
            appointmentScheduleOptionModelDataService.updateList(options.map { it.toModel() }, true)
                .map { it.map { it.toTransport() } }
                .recordResult(span)
        }

    suspend fun countOnSiteActiveByStaffIdAndType(
        staffId: UUID,
        type: AppointmentScheduleType
    ): Result<Int, Throwable> =
        appointmentScheduleOptionModelDataService.count {
            where {
                this.staffId.eq(staffId) and
                        this.type.eq(type) and
                        this.active.eq(true) and
                        this.calendarProviderUnits.isNotEmpty()
            }
        }

    private suspend fun findActiveAndVisibleOnAppAndModelType(healthcareModelType: HealthcareModelType): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService
            .find {
                where {
                    active.eq(true) and
                            showOnApp.eq(true) and
                            modelType.eq(healthcareModelType)
                }
            }.mapEach { it.toTransport() }

    private suspend fun findActiveAndVisibleOnAppWithTypes(
        types: List<AppointmentScheduleType>,
        healthcareModelType: HealthcareModelType
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        appointmentScheduleOptionModelDataService.find {
            where {
                active.eq(true) and
                        showOnApp.eq(true) and
                        type.inList(types) and
                        modelType.eq(healthcareModelType)
            }
        }.mapEach { it.toTransport() }

    private suspend fun findActiveWithTypes(
        types: List<AppointmentScheduleType>,
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        cache.getList(
            "findScheduleOptionsActiveWithTypes-${types.joinToString("-")}",
            AppointmentScheduleOption::class,
        ) { appointmentScheduleOptionModelDataService.find {
            where { active.eq(true) and type.inList(types) } }.mapEach { it.toTransport() }.get()
        }.success()

    private suspend fun findActiveForSubSpecialties(
        subSpecialtyIds: List<UUID>,
        healthcareModelType: HealthcareModelType
    ): Result<List<AppointmentScheduleOption>, Throwable> =
        subSpecialtyIds.takeIf { it.isNotEmpty() }?.let {
            appointmentScheduleOptionModelDataService.find {
                where {
                    active.eq(true) and
                            this.subSpecialtyIds.containsAny(subSpecialtyIds) and
                            modelType.eq(healthcareModelType)
                }
            }.mapEach { it.toTransport() }
        } ?: emptyList<AppointmentScheduleOption>().success()

    private fun AppointmentScheduleOption.isValidForMember(
        healthcareTeam: HealthcareTeam?
    ): Boolean {
        val typeIsNotHealthcareTeamOrImmersion = type != HEALTHCARE_TEAM && type != IMMERSION

        val typeIsHealthcareTeamAndFromPhysicianOrNurseOfHealthcareTeam = type == HEALTHCARE_TEAM &&
                isFromPhysicianOrNurseOfHealthcareTeam(healthcareTeam)

        return typeIsNotHealthcareTeamOrImmersion ||
                typeIsHealthcareTeamAndFromPhysicianOrNurseOfHealthcareTeam
    }

    private fun AppointmentScheduleOption.isFromPhysicianOrNurseOfHealthcareTeam(healthcareTeam: HealthcareTeam?) =
        staffId == healthcareTeam?.physicianStaffId || staffId == healthcareTeam?.nurseStaffId

    private suspend fun filterAppointmentScheduleOptionsAssociatedToEventTypes(
        appointmentScheduleOptions: List<AppointmentScheduleOption>,
        appointmentScheduleEventTypesById: Map<UUID, AppointmentScheduleEventType>,
        fromReferral: Boolean = true,
        eventTypeProviderUnits: Map<UUID, EventTypeProviderUnits>,
        healthcareTeam: HealthcareTeam?,
        staffSchedules: Map<UUID, List<StaffSchedule>>,
    ) =
        appointmentScheduleOptions
            .filter { it.appointmentScheduleEventTypeId != null }
            .filterWithActiveEventTypes(appointmentScheduleEventTypesById)
            .enrichWithEventTypeInfo(eventTypeProviderUnits)
            .let { filteredOptions ->
                groupAppointmentScheduleOptionsForPoolScheduling(
                    filteredOptions,
                    appointmentScheduleEventTypesById,
                    staffSchedules
                )
            }.let {
                if (!fromReferral) filterForPersonLeague(it, appointmentScheduleEventTypesById, healthcareTeam)
                else it
            }

    private fun filterForPersonLeague(
        appointmentScheduleOptions: List<AppointmentScheduleOption>,
        appointmentScheduleEventTypesById: Map<UUID, AppointmentScheduleEventType>,
        healthcareTeam: HealthcareTeam?,
    ): List<AppointmentScheduleOption> =
        appointmentScheduleOptions.filter { appointmentScheduleOption ->
            if (appointmentScheduleOption.staffId == null) true
            else {
                appointmentScheduleEventTypesById[appointmentScheduleOption.appointmentScheduleEventTypeId]
                    ?.let {
                        it.membersRisk.isEmpty() || appointmentScheduleOption.isFromPhysicianOrNurseOfHealthcareTeam(
                            healthcareTeam
                        )
                    }
                    ?: true
            }
        }

    private fun groupAppointmentScheduleOptionsForPoolScheduling(
        appointmentScheduleOptions: List<AppointmentScheduleOption>,
        appointmentScheduleEventsById: Map<UUID, AppointmentScheduleEventType>,
        staffSchedules: Map<UUID, List<StaffSchedule>>,
    ): List<AppointmentScheduleOption> {
        val appointmentScheduleEventTypesForPool =
            appointmentScheduleEventsById.values.filter { it.isMultiProfessionalReferral }.map { it.id }
        val nominalAppointmentScheduleOptions = appointmentScheduleOptions.filter {
            it.appointmentScheduleEventTypeId == null || !appointmentScheduleEventTypesForPool.contains(it.appointmentScheduleEventTypeId)
        }.filterUnitsNominalOptions(staffSchedules)
        val poolAppointmentScheduleOptions = appointmentScheduleOptions.filter {
            it.appointmentScheduleEventTypeId != null &&
                    appointmentScheduleEventTypesForPool.contains(it.appointmentScheduleEventTypeId)
        }.groupBy { it.appointmentScheduleEventTypeId }
            .map { it.value.first().copy(staffId = null, specialistId = null, imageUrl = "") }

        return nominalAppointmentScheduleOptions + poolAppointmentScheduleOptions
    }

    private fun List<AppointmentScheduleOption>.filterWithActiveEventTypes(
        appointmentScheduleEventsById: Map<UUID, AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter { appointmentScheduleEventsById[it.appointmentScheduleEventTypeId]?.status == Status.ACTIVE }

    private suspend fun List<AppointmentScheduleOption>.enrichWithEventTypeInfo(
        eventTypeProviderUnitsByEventType: Map<UUID, EventTypeProviderUnits>,
    ): List<AppointmentScheduleOption> =
        pmap { option ->
            option.copy(
                calendarProviderUnits = eventTypeProviderUnitsByEventType[option.appointmentScheduleEventTypeId]
                    ?.let { eventTypeProviderUnits ->
                        eventTypeProviderUnits.providerUnits.map { providerUnit ->
                            CalendarProviderUnit(
                                providerUnitId = providerUnit.id,
                                calendarUrl = "",
                                name = providerUnit.name,
                                address = providerUnit.address?.formattedAddress()
                            )
                        }
                    }
            )
        }

    private fun filterBasedOnTeamModelAndRisk(
        options: List<AppointmentScheduleOption>,
        team: HealthcareTeam?,
        risk: Risk?,
        isNewPortfolio: Boolean,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>,
    ): List<AppointmentScheduleOption> =
        when (team?.type) {
            LEAGUE -> filterLeagueOptions(risk, isNewPortfolio, options, appointmentScheduleEventTypes)
            LEAN -> options.getForLeanMembers(team)
            else -> options.getForNonLeagueMembers(appointmentScheduleEventTypes)
        }

    private fun filterLeagueOptions(
        risk: Risk?,
        isNewPortfolio: Boolean,
        options: List<AppointmentScheduleOption>,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        if (isNewPortfolio) options
        else {
            val testAppointmentScheduleOptions = options.filter { it.type == TEST }
            val filteredOptionsByRisk = when {
                isHighRiskForScheduling(risk) -> options.getForHighRiskLeagueMembers(appointmentScheduleEventTypes)
                isLowRiskForScheduling(risk) -> options.getForLowRiskLeagueMembers(appointmentScheduleEventTypes)
                isMediumRiskForScheduling(risk) -> options.getForMediumRiskLeagueMembers(appointmentScheduleEventTypes)
                else -> options.getForNoRiskLeagueMembers(appointmentScheduleEventTypes)
            }

            filteredOptionsByRisk + testAppointmentScheduleOptions
        }

    private fun isHighRiskForScheduling(risk: Risk?) =
        listOf(RiskDescription.HIGH_RISK).contains(risk?.riskDescription)

    private fun isMediumRiskForScheduling(risk: Risk?) =
        listOf(RiskDescription.MEDIUM_RISK).contains(risk?.riskDescription)

    private fun isLowRiskForScheduling(risk: Risk?) =
        listOf(RiskDescription.LOW_RISK).contains(risk?.riskDescription)

    private fun List<AppointmentScheduleOption>.getForHighRiskLeagueMembers(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter {
            appointmentScheduleEventTypes.getIdsForHighRiskLeagueMembers().contains(it.appointmentScheduleEventTypeId)
        }

    private fun List<AppointmentScheduleOption>.getForMediumRiskLeagueMembers(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter {
            appointmentScheduleEventTypes.getIdsForMediumRiskLeagueMembers().contains(it.appointmentScheduleEventTypeId)
        }

    private fun List<AppointmentScheduleOption>.getForLowRiskLeagueMembers(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter {
            appointmentScheduleEventTypes.getIdsForLowRiskLeagueMembers().contains(it.appointmentScheduleEventTypeId)
        }

    private fun List<AppointmentScheduleOption>.getForNoRiskLeagueMembers(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter {
            appointmentScheduleEventTypes.getIdsForNoRiskLeagueMembers().contains(it.appointmentScheduleEventTypeId)
        }

    private fun List<AppointmentScheduleOption>.getForNonLeagueMembers(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>
    ): List<AppointmentScheduleOption> =
        filter {
            !appointmentScheduleEventTypes.getIdsForLowRiskLeagueMembers()
                .contains(it.appointmentScheduleEventTypeId) &&
                    !appointmentScheduleEventTypes.getIdsForHighRiskLeagueMembers()
                        .contains(it.appointmentScheduleEventTypeId) &&
                    !appointmentScheduleEventTypes.getIdsForNoRiskLeagueMembers()
                        .contains(it.appointmentScheduleEventTypeId) &&
                    !appointmentScheduleEventTypes.getIdsForMediumRiskLeagueMembers()
                        .contains(it.appointmentScheduleEventTypeId)
        }

    private fun List<AppointmentScheduleOption>.getForLeanMembers(
        healthcareTeam: HealthcareTeam
    ): List<AppointmentScheduleOption> {
        val testOptions = filter { testTypes().contains(it.type) }
        val optionsByTeam = filter { it.staffId == healthcareTeam.physicianStaffId }
        return optionsByTeam + testOptions
    }


    private fun List<AppointmentScheduleEventType>.getIdsForHighRiskLeagueMembers() =
        filter { it.membersRisk.contains(AppointmentScheduleEventTypeMemberRisk.HIGH_RISK) }
            .map { it.id }

    private fun List<AppointmentScheduleEventType>.getIdsForLowRiskLeagueMembers() =
        filter { it.membersRisk.contains(AppointmentScheduleEventTypeMemberRisk.LOW_RISK) }
            .map { it.id }

    private fun List<AppointmentScheduleEventType>.getIdsForNoRiskLeagueMembers() =
        filter { it.membersRisk.contains(AppointmentScheduleEventTypeMemberRisk.NO_RISK) }
            .map { it.id }

    private fun List<AppointmentScheduleEventType>.getIdsForMediumRiskLeagueMembers() =
        filter { it.membersRisk.contains(AppointmentScheduleEventTypeMemberRisk.MEDIUM_RISK) }
            .map { it.id }

    private fun List<AppointmentScheduleOption>.listByHealthcareTeamAndCheckFromReferral(
        healthcareTeam: HealthcareTeam?,
        fromReferral: Boolean,
    ): List<AppointmentScheduleOption> =
        if (healthcareTeam?.isLeague() == true || healthcareTeam?.isLean() == true || fromReferral) this
        else filter { option -> option.isValidForMember(healthcareTeam) }

    private fun List<AppointmentScheduleOption>.filterOptionsByPersonAge(
        memberIsChildren: Boolean,
    ): List<AppointmentScheduleOption> {
        val ageRatingForFilter = listOf(
            AgeRatingType.BOTH,
            if (memberIsChildren) AgeRatingType.CHILDREN else AgeRatingType.ADULT
        )

        return this.filter { ageRatingForFilter.contains(it.ageRating) }
    }

    private suspend fun List<AppointmentScheduleOption>.listAppointmentScheduleEventTypes(): List<AppointmentScheduleEventType> =
        this.mapNotNull { it.appointmentScheduleEventTypeId }
            .distinct()
            .takeIf { it.isNotEmpty() }
            ?.let { appointmentScheduleEventTypeService.getByIds(it).get() }
            ?: emptyList()

    private suspend fun List<AppointmentScheduleEventType>.listEventTypeProviderUnits(): List<EventTypeProviderUnits> =
        this.takeIf { it.isNotEmpty() }
            ?.map { it.id }
            ?.let { eventTypeProviderUnitService.getForEventTypesWithProviderUnitData(it).get() }
            ?: emptyList()

    private suspend fun createNewAssociate(
        appointmentScheduleEventTypeId: UUID,
        staffId: UUID
    ): Result<AppointmentScheduleOption, Throwable> =
        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            .flatMapPair { staffService.get(staffId) }
            .map { (staff, eventType) -> AppointmentScheduleOptionCreator.convert(eventType, staff) }
            .flatMap { add(it) }

    private suspend fun updateOldAssociate(
        appointmentScheduleOption: AppointmentScheduleOption,
        appointmentScheduleEventTypeId: UUID,
        staffId: UUID
    ): Result<AppointmentScheduleOption, Throwable> =
        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            .flatMapPair { staffService.get(staffId) }
            .map { (staff, eventType) -> AppointmentScheduleOptionCreator.convert(eventType, staff) }
            .map { it.copy(id = appointmentScheduleOption.id, version = appointmentScheduleOption.version) }
            .flatMap { update(it) }


    @OptIn(WithFilterPredicateUsage::class)
    private fun QueryBuilder<FieldOptions, OrderingOptions>.byFilter(
        filters: AppointmentScheduleOptionFilters
    ): QueryBuilder<FieldOptions, OrderingOptions> =
        where {
            basePredicateForFilters()
                .withFilter(filters.titlePrefix) { this.title.like(it) }
                .withFilter(filters.specialistIds) { this.healthCommunitySpecialistId.inList(it) }
                .withFilter(filters.staffIds) { this.staffId.inList(it) }
                .withFilter(filters.appointmentScheduleEventTypeIds) { this.appointmentScheduleEventTypeId.inList(it) }
                .withFilter(filters.specialtyId) { this.specialtyId.eq(it) }
                .withFilter(filters.subSpecialtyIds) { this.subSpecialtyIds.containsAny(it) }
                .withFilter(filters.appointmentScheduleEventTypeId) { this.appointmentScheduleEventTypeId.eq(it) }
                .withFilter(filters.ageRating) { this.ageRating.inList(it) }
                .withFilter(filters.types) { this.type.inList(it) }
                .withFilter(filters.active) { this.active.eq(it) }!!

        }.let { query ->
            filters.range?.let { range ->
                query
                    .offset { range.first }
                    .limit { range.count() }
            } ?: query
        }.orderBy { createdAt }
            .sortOrder { filters.sortOrder }
}

