package br.com.alice.schedule.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.schedule.client.SchedulePreferenceService
import br.com.alice.schedule.model.events.SchedulePreferenceCreatedEvent
import br.com.alice.schedule.model.events.StaffScheduleCreatedEvent
import br.com.alice.schedule.services.StaffSchedulePreferenceServiceImpl
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class SchedulePreferenceConsumerTest : ConsumerTest() {

    private val staffService: StaffService = mockk()
    private val schedulePreferenceService: SchedulePreferenceService = mockk()
    private val staffSchedulePreferenceService: StaffSchedulePreferenceServiceImpl = mockk()
    private val consumer = SchedulePreferenceConsumer(
        staffService,
        schedulePreferenceService,
        staffSchedulePreferenceService
    )

    private val staff = TestModelFactory.buildStaff().copy(searchTokens = "asd")
    private val schedulePreference = TestModelFactory.buildSchedulePreference(staffId = staff.id)
    private val staffSchedulePreference = TestModelFactory.buildStaffSchedulePreference(staffId = staff.id)
    private val staffSchedule = TestModelFactory.buildStaffSchedule()

    @Test
    fun `#createStaffSchedulePreference should create Staff Schedule Preference`() = runBlocking {
        val event = SchedulePreferenceCreatedEvent(
            schedulePreference,
        )

        coEvery { staffService.get(staff.id) } returns staff.success()
        coEvery { staffSchedulePreferenceService.getByStaffId(staff.id) } returns NotFoundException().failure()
        coEvery {
            staffSchedulePreferenceService.add(
                match {
                    it.schedulePreferenceId == schedulePreference.id &&
                            it.staffPhoto == staff.profileImageUrl &&
                            it.staffName == staff.fullName &&
                            it.staffEmail == staff.email &&
                            it.staffId == staff.id &&
                            it.staffRole == staff.role.toString()
                }
            )
        } returns staffSchedulePreference.success()

        val result = consumer.createStaffSchedulePreference(event)
        assertThat(result).isSuccessWithData(staffSchedulePreference)
    }

    @Test
    fun `#updateSchedulePreferenceHasStaffSchedules should update has staff schedules schedule preference property`() =
        runBlocking {
            val event = StaffScheduleCreatedEvent(staffSchedule)

            coEvery {
                schedulePreferenceService.getByStaffId(staffId = staffSchedule.staffId)
            } returns schedulePreference.success()
            coEvery {
                schedulePreferenceService.update(
                    schedulePreference.copy(hasStaffSchedules = true)
                )
            } returns schedulePreference.success()
            coEvery {
                staffSchedulePreferenceService.getByStaffId(staffId = staffSchedule.staffId)
            } returns staffSchedulePreference.success()
            coEvery {
                staffSchedulePreferenceService.update(
                    staffSchedulePreference.copy(hasStaffSchedules = true)
                )
            } returns staffSchedulePreference.success()

            val result = consumer.updateHasStaffSchedules(event)
            assertThat(result).isSuccessWithData(true)
        }
}
