package br.com.alice.itau.converter

import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.ItauPayment
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauBoletoAliceBankAccountCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoBoletoDataCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoConfigDivergentReceipt
import br.com.alice.itau.clients.models.ItauBoletoCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoCreateResponse
import br.com.alice.itau.clients.models.ItauBoletoDataCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoDescriptionCharge
import br.com.alice.itau.clients.models.ItauBoletoFineCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoGetResponse
import br.com.alice.itau.clients.models.ItauBoletoIndividualCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoInterestCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerPersonCode
import br.com.alice.itau.clients.models.ItauBoletoPayerPersonCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoPayerTypeCreateRequest
import br.com.alice.itau.clients.models.ItauBoletoStatus
import br.com.alice.itau.clients.models.ItauPaymentCreatedExternalIds
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.BankSlipInfo
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.models.PixInfo
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter

fun ItauBoletoCreateResponse.toAcquirerCreatePaymentResponse(itauPayment: ItauPayment): Pair<AcquirerCreatePaymentResponse, ItauPaymentCreatedExternalIds> =
    Pair(
        AcquirerCreatePaymentResponse(
            id = itauPayment.id.toString(),
            source = InvoicePaymentSource.ITAU,
            externalUrl = null,
            status = PaymentStatus.PENDING,
            bankSlip = BankSlipInfo(
                digitableLine = this.data.boletoData.individualBoleto.first().digitableLine,
                barcodeData = this.data.boletoData.individualBoleto.first().barcodeData
            )
        ), ItauPaymentCreatedExternalIds(
            pixId = null,
            boletoId = this.data.boletoData.individualBoleto.first().id
        )
    )

fun Int.toOurNumberString(): String = this.toString().padStart(8, '0')

fun PaymentRequestInput.shouldIgnoreInterestAndFineIfFirstPayment(): Boolean {
    if (this.isFirstPayment) {
        return true
    }
    return FeatureService.get(FeatureNamespace.MONEY_IN, "ignore-interest-and-fine", false)
}

fun PaymentRequestInput.toItauBoletoCreateRequest(
    itauPayment: ItauPayment
): ItauBoletoCreateRequest =
    ItauBoletoCreateRequest(
        data = ItauBoletoDataCreateRequest(
            beneficiary = ItauBoletoAliceBankAccountCreateRequest(
                ServiceConfig.itauBoletoConfig.aliceAccountKey
            ),
            boletoData = ItauBoletoBoletoDataCreateRequest(
                descriptionCharge = ItauBoletoDescriptionCharge.BOLECODE.code,
                payer = ItauBoletoPayerCreateRequest(
                    person = ItauBoletoPayerPersonCreateRequest(
                        name = this.payer.name,
                        personType = ItauBoletoPayerTypeCreateRequest(
                            personCode = when (this.payer.type) {
                                BillingAccountablePartyType.NATURAL_PERSON -> ItauBoletoPayerPersonCode.FISICA.code
                                BillingAccountablePartyType.LEGAL_PERSON -> ItauBoletoPayerPersonCode.JURIDICA.code
                            },
                            cnpj = if (this.payer.type == BillingAccountablePartyType.LEGAL_PERSON) {
                                this.payer.nationalId
                            } else null,
                            cpf = if (this.payer.type == BillingAccountablePartyType.NATURAL_PERSON) {
                                this.payer.nationalId
                            } else null
                        ),
                    ),
                ),
                interest = if (shouldIgnoreInterestAndFineIfFirstPayment()) null else ItauBoletoInterestCreateRequest(),
                fine = if (shouldIgnoreInterestAndFineIfFirstPayment()) null else ItauBoletoFineCreateRequest(),
                divergentReceipt = ItauBoletoConfigDivergentReceipt(),
                individualBoleto = listOf(
                    ItauBoletoIndividualCreateRequest(
                        ourNumber = itauPayment.ourNumber!!.toOurNumberString(),
                        dueDate = this.dueDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        titleValue = this.totalAmount.toItauBoletoValue(),
                        paymentDeadline = this.dueDate.plusYears(2).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    )
                )
            )
        )
    )

fun ItauBoletoStatus.toPaymentStatus(): PaymentStatus = when (this) {
    ItauBoletoStatus.PENDING -> PaymentStatus.PENDING
    ItauBoletoStatus.CANCELED -> PaymentStatus.CANCELED
    ItauBoletoStatus.PAID -> PaymentStatus.APPROVED
}

fun ItauBoletoGetResponse.toAcquirerGetPaymentResponse() = AcquirerGetPaymentResponse(
    id = this.data.first().id,
    status = this.data.first().boletoData.individualBoleto.first().status.toPaymentStatus(),
    bankSlip = BankSlipInfo(
        digitableLine = this.data.first().boletoData.individualBoleto.first().digitableLine,
        barcodeData = this.data.first().boletoData.individualBoleto.first().barcodeData,
    ),
    pix = this.data.first().boletoData.pixData?.let {
        PixInfo(
            copyAndPaste = it.pixCopyAndPaste,
        )
    },
    externalUrl = null,
    totalCents = (this.data.first().boletoData.individualBoleto.first().titleValue.toBigDecimal() * BigDecimal(100)).toInt(),
    dueDate = LocalDate.parse(this.data.first().boletoData.individualBoleto.first().dueDate),
)
