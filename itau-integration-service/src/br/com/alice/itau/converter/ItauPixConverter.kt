package br.com.alice.itau.converter

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.data.layer.models.ItauPayment
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauPaymentCreatedExternalIds
import br.com.alice.itau.clients.models.ItauPixCalendar
import br.com.alice.itau.clients.models.ItauPixCreateRequest
import br.com.alice.itau.clients.models.ItauPixCreateResponse
import br.com.alice.itau.clients.models.ItauPixGetCalendar
import br.com.alice.itau.clients.models.ItauPixGetResponse
import br.com.alice.itau.clients.models.ItauPixPersonInfo
import br.com.alice.itau.clients.models.ItauPixStatus
import br.com.alice.itau.clients.models.ItauPixValue
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.AcquirerGetPaymentResponse
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.models.PixInfo
import java.math.BigDecimal
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

fun ItauPixStatus.toInvoicePaymentStatus(): PaymentStatus =
    when (this) {
        ItauPixStatus.ATIVA -> PaymentStatus.PENDING
        ItauPixStatus.CONCLUIDA -> PaymentStatus.APPROVED
        ItauPixStatus.REMOVIDA_PELO_USUARIO_RECEBEDOR,
        ItauPixStatus.REMOVIDO_PELO_PSP -> PaymentStatus.CANCELED
    }

fun ItauPixCreateResponse.toAcquirerCreatePaymentResponse(itauPayment: ItauPayment): Pair<AcquirerCreatePaymentResponse, ItauPaymentCreatedExternalIds> =
    Pair(
        AcquirerCreatePaymentResponse(
            id = itauPayment.id.toString(),
            source = InvoicePaymentSource.ITAU,
            externalUrl = null,
            status = this.paymentStatus.toInvoicePaymentStatus(),
            pix = PixInfo(
                copyAndPaste = this.pixCopiaECola
            )
        ), ItauPaymentCreatedExternalIds(
            pixId = this.txid,
            boletoId = null
        )
    )

fun LocalDate.toExpirationInSeconds(): String {
    val now = LocalDate.now().atBeginningOfTheDay()
    val dueDate = this.atEndOfTheDay()
    return Duration.between(now, dueDate).toSeconds().toString()
}

fun ItauPixGetCalendar.toDueDate(): LocalDate {
    val criacaoInstant = Instant.parse(createdAt)
    val expiracaoInstant = criacaoInstant.plusSeconds(expirationInSeconds)
    return expiracaoInstant.atZone(ZoneId.systemDefault()).toLocalDate()
}

fun PaymentRequestInput.toItauPixCreateRequest(): ItauPixCreateRequest =
    ItauPixCreateRequest(
        expiration = ItauPixCalendar(
            expirationInSeconds = this.dueDate.toExpirationInSeconds()
        ),
        value = ItauPixValue(
            amount = this.totalAmount.toString()
        ),
        alicePixKey = ServiceConfig.itauPixConfig.alicePixKey,
        personInfo = ItauPixPersonInfo(
            cpf = payer.type.takeIf { it == BillingAccountablePartyType.NATURAL_PERSON }?.let { payer.nationalId },
            cnpj = payer.type.takeIf { it == BillingAccountablePartyType.LEGAL_PERSON }?.let { payer.nationalId },
            name = payer.name
        )
    )

fun ItauPixGetResponse.toAcquirerGetPaymentResponse(): AcquirerGetPaymentResponse =
    AcquirerGetPaymentResponse(
        id = this.txid,
        status = this.paymentStatus.toInvoicePaymentStatus(),
        bankSlip = null,
        pix = PixInfo(
            copyAndPaste = this.copyAndPaste,
        ),
        externalUrl = null,
        totalCents = (this.titleValue.amount.toBigDecimal() * BigDecimal("100")).toInt(),
        dueDate = this.expiration.toDueDate()
    )
