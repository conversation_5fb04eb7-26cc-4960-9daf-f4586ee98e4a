package br.com.alice.itau.converters

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.data.layer.models.InvoicePaymentSource
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauPaymentCreatedExternalIds
import br.com.alice.itau.clients.models.ItauPixCalendar
import br.com.alice.itau.clients.models.ItauPixCreateRequest
import br.com.alice.itau.clients.models.ItauPixCreateResponse
import br.com.alice.itau.clients.models.ItauPixPersonInfo
import br.com.alice.itau.clients.models.ItauPixStatus
import br.com.alice.itau.clients.models.ItauPixValue
import br.com.alice.itau.converter.toAcquirerCreatePaymentResponse
import br.com.alice.itau.converter.toExpirationInSeconds
import br.com.alice.itau.converter.toInvoicePaymentStatus
import br.com.alice.itau.converter.toItauPixCreateRequest
import br.com.alice.moneyin.models.AcquirerCreatePaymentResponse
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import br.com.alice.moneyin.models.PixInfo
import org.assertj.core.api.Assertions
import java.time.LocalDate
import kotlin.test.Test

class ItauPixConverterTest {
    private val itauPaymentCreateResponse = ItauPixCreateResponse(
        txid = "txId",
        pixCopiaECola = "pixCopiaECola",
        paymentStatus = ItauPixStatus.ATIVA
    )
    private val itauPayment = TestModelFactory.buildItauPayment()

    @Test
    fun `#toInvoicePaymentStatus should convert itau pix status to PaymentStatus`() {
        val expectedStatus = PaymentStatus.PENDING
        val statusConverted = itauPaymentCreateResponse.paymentStatus.toInvoicePaymentStatus()

        Assertions.assertThat(statusConverted).isEqualTo(expectedStatus)
    }

    @Test
    fun `#toAcquirerCreatePaymentResponse should convert itauPixCreateResponse to AcquirerCreatePaymentResponse`() {
        val expected = Pair(
            AcquirerCreatePaymentResponse(
                id = itauPayment.id.toString(),
                source = InvoicePaymentSource.ITAU,
                externalUrl = null,
                status = itauPaymentCreateResponse.paymentStatus.toInvoicePaymentStatus(),
                pix = PixInfo(
                    copyAndPaste = itauPaymentCreateResponse.pixCopiaECola
                )
            ), ItauPaymentCreatedExternalIds(
                pixId = itauPaymentCreateResponse.txid,
                boletoId = null
            )
        )

        val convertedResponse = itauPaymentCreateResponse.toAcquirerCreatePaymentResponse(itauPayment)

        Assertions.assertThat(convertedResponse).isEqualTo(expected)
    }

    @Test
    fun `#toItauPixCreateRequest should convert paymentRequestInput to ItauPixCreateRequest`() {
        val payer = PaymentRequestInput.Payer(
            id = RangeUUID.generate(),
            nationalId = "***********",
            name = "Naruto Uzumaki",
            address = null,
            email = "<EMAIL>",
            type = BillingAccountablePartyType.LEGAL_PERSON
        )

        val paymentRequestInput = PaymentRequestInput(
            orderId = RangeUUID.generate(),
            referenceDates = emptyList(),
            dueDate = LocalDate.now(),
            payer = payer,
            people = emptyList(),
            isFirstPayment = false,
            method = PaymentMethod.PIX,
            installment = 0,
            totalInstallments = 1,
            totalAmount = 100.toBigDecimal()
        )

        val expected = ItauPixCreateRequest(
            expiration = ItauPixCalendar(
                expirationInSeconds = paymentRequestInput.dueDate.toExpirationInSeconds()
            ),
            value = ItauPixValue(
                amount = paymentRequestInput.totalAmount.toString()
            ),
            alicePixKey = ServiceConfig.itauPixConfig.alicePixKey,
            personInfo = ItauPixPersonInfo(
                cpf = payer.type.takeIf { it == BillingAccountablePartyType.NATURAL_PERSON }?.let { payer.nationalId },
                cnpj = payer.type.takeIf { it == BillingAccountablePartyType.LEGAL_PERSON }?.let { payer.nationalId },
                name = payer.name
            )
        )

        val converted = paymentRequestInput.toItauPixCreateRequest()

        Assertions.assertThat(expected).isEqualTo(converted)
    }

    @Test
    fun `#toExpirationInSeconds should convert expiration in seconds`() {
        val date1 = LocalDate.now()

        val expected = "86399" // 23 hours and 59 minutes in seconds
        val result = date1.toExpirationInSeconds()

        Assertions.assertThat(result).isEqualTo(expected)
    }
}
