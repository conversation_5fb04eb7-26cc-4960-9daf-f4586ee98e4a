package br.com.alice.nullvs.consumers.member

import br.com.alice.business.events.BeneficiaryUpdatedEvent
import br.com.alice.business.events.NewBeneficiaryRelationshipAssignedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.exceptions.AutoRetryableException
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Person
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.consumers.ConsumerTest
import br.com.alice.nullvs.events.NullvsMemberBatchRequestEvent
import br.com.alice.nullvs.exceptions.CityCodeNotFoundException
import br.com.alice.nullvs.exceptions.DependsOnModelException
import br.com.alice.nullvs.exceptions.NotFullMothersNameException
import br.com.alice.nullvs.exceptions.NullANSFieldException
import br.com.alice.nullvs.exceptions.NullPersonalFieldException
import br.com.alice.nullvs.exceptions.NullTotvsContractException
import br.com.alice.nullvs.exceptions.PersonAddressFieldException
import br.com.alice.nullvs.exceptions.SkipException
import br.com.alice.nullvs.exceptions.TestPersonalRegisterException
import br.com.alice.nullvs.exceptions.TotvsClientNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsMemberNotFoundByInternalIdException
import br.com.alice.nullvs.exceptions.TotvsParentMemberNotFoundByInternalIdException
import br.com.alice.nullvs.models.Meta
import br.com.alice.nullvs.models.TotvsCityCode
import br.com.alice.nullvs.models.TotvsGroupCompany
import br.com.alice.nullvs.models.TotvsRelationType
import br.com.alice.nullvs.models.TotvsUser
import br.com.alice.nullvs.models.member.GracePeriodType
import br.com.alice.nullvs.models.member.NullvsMemberBatchRequest
import br.com.alice.nullvs.models.member.TotvsMemberRequest
import br.com.alice.nullvs.services.internals.TotvsMemberIntegrationService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import br.com.alice.data.layer.models.GracePeriodType as BeneficiaryGracePeriodType

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BeneficiaryConsumerTest : ConsumerTest() {
    private val totvsMemberIntegrationService: TotvsMemberIntegrationService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val memberService: MemberService = mockk()

    private val consumer =
        BeneficiaryConsumer(totvsMemberIntegrationService, memberService, LocalProducer)

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            NullANSFieldException(""),
            NullPersonalFieldException(""),
            NotFullMothersNameException(""),
            TestPersonalRegisterException(""),
            PersonAddressFieldException(""),
            CityCodeNotFoundException(""),
            SkipException(""),
            TotvsParentMemberNotFoundByInternalIdException(""),
            DependsOnModelException(UUID.randomUUID(), InternalModelType.MEMBER)
        )
    }

    @BeforeTest
    fun setup() {
        super.before()
        LocalProducer.clearMessages()
    }

    private fun buildMetadata(
        event: NewBeneficiaryRelationshipAssignedEvent,
        beneficiary: Beneficiary,
        sequence: Int
    ): Meta {
        val eventId = RangeUUID.generateFromUUID(event.messageId, (sequence).toByte())

        return Meta(
            eventId = eventId,
            eventName = event.name,
            internalModelName = InternalModelType.MEMBER,
            internalId = beneficiary.memberId,
            externalModelName = ExternalModelType.BENEFICIARY,
            integratedAt = event.eventDate,
            originalTopic = NewBeneficiaryRelationshipAssignedEvent.name,
            integrationEventName = NullvsMemberBatchRequestEvent.name,
        )
    }

    private fun buildNullvsMemberBatchRequest(
        person: Person,
        member: Member,
        meta: Meta,
        actionType: NullvsActionType
    ) = NullvsMemberBatchRequest(
        meta,
        actionType,
        TotvsMemberRequest(
            company = TotvsGroupCompany.ALICE_INDIVIDUAL,
            client = "member",
            createdAt = LocalDateTime.now(),
            ANSProductId = "ans-product-id",
            idPayload = 1,
            beneficiaries = listOf(
                TotvsMemberRequest.TotvsBeneficiary(
                    userType = TotvsUser.HOLDER,
                    email = person.email,
                    fullName = person.fullRegisterName,
                    fullSocialName = person.fullSocialName,
                    aliceId = member.id,
                    nationalId = person.nationalId.onlyNumbers(),
                    identityDocument = person.identityDocument,
                    mothersName = person.mothersName!!,
                    addressPostalCode = person.mainAddress!!.postalCode!!.onlyDigits(),
                    addressStreet = person.mainAddress!!.street,
                    addressNumber = person.mainAddress!!.number,
                    addressComplement = person.mainAddress!!.complement,
                    addressNeighborhood = person.mainAddress!!.neighbourhood!!,
                    addressCity = TotvsCityCode("3550308", "SAO PAULO", "SP"),
                    addressState = person.mainAddress!!.state.toString(),
                    phoneNumber = person.phoneNumber!!,
                    dateOfBirth = person.dateOfBirth!!,
                    createdAt = person.createdAt,
                    parentBeneficiaryRelationType = TotvsRelationType.HOLDER,
                    sex = Sex.FEMALE,
                    cnsNumber = person.cnsNumber,
                    ccoCodeANS = null,
                    ANSProductId = "ans-id",
                    canceledReason = null,
                    canceledAt = null,
                    cpts = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.CPT(
                            cid = "XPTO",
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 730L
                        )
                    ),
                    gracePeriods = listOf(
                        TotvsMemberRequest.TotvsBeneficiary.GracePeriod(
                            type = GracePeriodType.BIRTH,
                            startedAt = LocalDate.of(2023, 6, 1),
                            periodInDays = 300L
                        )
                    ),
                ),
            ),
        ),
    )

    @Nested
    inner class NewRelationshipAssigned {
        private val person = TestModelFactory.buildPerson(
            mothersName = "Mother's name",
            phoneNumber = "phone",
            dateOfBirth = LocalDateTime.of(1989, 9, 14, 16, 20),
        )

        private val previousMember = TestModelFactory.buildMember(personId = person.id)
        private val previousBeneficiary = TestModelFactory.buildBeneficiary(memberId = previousMember.id)
        private val newMember = TestModelFactory.buildMember(personId = person.id)
        private val newBeneficiary = TestModelFactory.buildBeneficiary(memberId = newMember.id)
        private val event = NewBeneficiaryRelationshipAssignedEvent(previousBeneficiary, newBeneficiary)

        private val metadataPreviousMember = buildMetadata(event, previousBeneficiary, 1)
        private val metadataNewMember = buildMetadata(event, newBeneficiary, 2)

        private val previousMemberBatchRequest = buildNullvsMemberBatchRequest(
            person, previousMember, metadataPreviousMember, NullvsActionType.CANCEL
        )

        private val newMemberBatchRequest = buildNullvsMemberBatchRequest(
            person, previousMember, metadataNewMember, NullvsActionType.CREATE
        )

//        @Test
//        fun `#should produce two NullvsMemberBatchRequestEvent to cancel and then create beneficiary with new beneficiary relationship is assigned - in this order`() =
//            runBlocking {
//                coEvery {
//                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
//                        metadataPreviousMember,
//                        previousMember
//                    )
//                } returns previousMemberBatchRequest
//                coEvery {
//                    totvsMemberIntegrationService.createBeneficiaryTotvs(
//                        metadataNewMember,
//                        newMember,
//                    )
//                } returns newMemberBatchRequest
//
//                coEvery { memberService.get(previousMember.id) } returns previousMember
//                coEvery { memberService.get(newMember.id) } returns newMember
//
//                val result = consumer.newRelationshipAssigned(event)
//
//                ResultAssert.assertThat(result).isSuccess()
//
//                val cancelEventPayload = LocalProducer.events[0].payload
//                val createEventPayload = LocalProducer.events[1].payload
//
//                Assertions.assertThat((cancelEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
//                    .isEqualTo(NullvsActionType.CANCEL)
//                Assertions.assertThat((createEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
//                    .isEqualTo(NullvsActionType.CREATE)
//
//                Assertions.assertThat(LocalProducer.events).hasSize(2)
//                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue
//
//                coVerifyOnce {
//                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
//                        metadataPreviousMember,
//                        previousMember
//                    )
//                }
//                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(metadataNewMember, newMember) }
//            }
//
//        @Test
//        fun `#should return an AutoRetryableException in case of NullTotvsContractException when creating beneficiary`() =
//            runBlocking {
//                coEvery {
//                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
//                        metadataPreviousMember,
//                        previousMember
//                    )
//                } returns previousMemberBatchRequest
//                coEvery {
//                    totvsMemberIntegrationService.createBeneficiaryTotvs(
//                        metadataNewMember,
//                        newMember,
//                    )
//                } returns NullTotvsContractException(newMember.id).failure()
//
//                coEvery { memberService.get(previousMember.id) } returns previousMember
//                coEvery { memberService.get(newMember.id) } returns newMember
//
//                val result = consumer.newRelationshipAssigned(event)
//
//                ResultAssert.assertThat(result).isFailureOfType(AutoRetryableException::class)
//                assert(result.component2()!!.cause is NullTotvsContractException)
//
//                val cancelEventPayload = LocalProducer.events[0].payload
//
//                Assertions.assertThat((cancelEventPayload as NullvsMemberBatchRequestEvent.Payload).request.action)
//                    .isEqualTo(NullvsActionType.CANCEL)
//
//                Assertions.assertThat(LocalProducer.events).hasSize(1)
//                Assertions.assertThat(LocalProducer.hasEvent(NullvsMemberBatchRequestEvent.name)).isTrue
//
//                coVerifyOnce { totvsMemberIntegrationService.cancelBeneficiaryTotvs(any(), any()) }
//                coVerifyOnce { totvsMemberIntegrationService.createBeneficiaryTotvs(any(), any()) }
//            }

        @ParameterizedTest(name = "should return success when exception {0} happens to avoid dlq")
        @MethodSource("br.com.alice.nullvs.consumers.member.BeneficiaryConsumerTest#exceptions")
        fun `should return success when NullANSFieldException happens to avoid dlq`(exception: BadRequestException) =
            runBlocking<Unit> {
                coEvery {
                    totvsMemberIntegrationService.cancelBeneficiaryTotvs(
                        metadataPreviousMember,
                        previousMember
                    )
                } returns previousMemberBatchRequest
                coEvery {
                    totvsMemberIntegrationService.createBeneficiaryTotvs(
                        metadataNewMember,
                        newMember,
                    )
                } returns exception.failure()

                coEvery { memberService.get(previousMember.id) } returns previousMember
                coEvery { memberService.get(newMember.id) } returns newMember

                val result = consumer.newRelationshipAssigned(event)

                ResultAssert.assertThat(result).isSuccess()
            }
    }

//    @Nested
//    inner class BeneficiaryUpdated {
//        val person = TestModelFactory.buildPerson(
//            mothersName = "Mother's name",
//            phoneNumber = "phone",
//            dateOfBirth = LocalDateTime.of(1989, 9, 14, 16, 20),
//        )
//        val member = TestModelFactory.buildMember(
//            personId = person.id,
//            status = MemberStatus.ACTIVE,
//        )
//        val beneficiary = TestModelFactory.buildBeneficiary(
//            memberId = member.id,
//            personId = person.id,
//            gracePeriodType = BeneficiaryGracePeriodType.TOTAL_GRACE_PERIOD,
//        )
//
//        @Test
//        fun `#updateBeneficiaryTotvs should return success when updating beneficiary`(): Unit = runBlocking {
//            val updatedBeneficiary = beneficiary.copy(
//                gracePeriodType = BeneficiaryGracePeriodType.TOTAL_EXEMPTION,
//            )
//
//            val event = BeneficiaryUpdatedEvent(updatedBeneficiary)
//
//            val metadata = Meta(
//                eventId = event.messageId,
//                eventName = event.name,
//                internalModelName = InternalModelType.MEMBER,
//                internalId = member.id,
//                externalModelName = ExternalModelType.BENEFICIARY,
//                integratedAt = event.eventDate,
//                originalTopic = BeneficiaryUpdatedEvent.name,
//                integrationEventName = NullvsMemberBatchRequestEvent.name,
//            )
//
//            val batchRequest = buildNullvsMemberBatchRequest(
//                person, member, metadata, NullvsActionType.UPDATE
//            )
//
//            val producerResult = ProducerResult(
//                producedAt = LocalDateTime.now(),
//                topic = "BeneficiaryUpdatedEvent",
//                offset = 30.toLong(),
//            )
//
//
//            coEvery { memberService.get(beneficiary.memberId) } returns member
//            coEvery { totvsMemberIntegrationService.updateBeneficiaryTotvs(metadata, member) } returns batchRequest
//            coEvery {
//                kafkaProducerService.produce(
//                    NullvsMemberBatchRequestEvent(batchRequest),
//                    beneficiary.personId.toString()
//                )
//            } returns producerResult
//
//            val result = consumer.updateBeneficiaryTotvs(event)
//
//            ResultAssert.assertThat(result).isSuccess()
//        }
//
//        @Test
//        fun `#updateBeneficiaryTotvs should ignore event when member status is PENDING`(): Unit = runBlocking {
//            val updatedBeneficiary = beneficiary.copy(
//                gracePeriodType = BeneficiaryGracePeriodType.TOTAL_EXEMPTION,
//            )
//
//            val event = BeneficiaryUpdatedEvent(updatedBeneficiary)
//            val pendingMember = member.copy(status = MemberStatus.PENDING)
//
//            val metadata = Meta(
//                eventId = event.messageId,
//                eventName = event.name,
//                internalModelName = InternalModelType.MEMBER,
//                internalId = pendingMember.id,
//                externalModelName = ExternalModelType.BENEFICIARY,
//                integratedAt = event.eventDate,
//                originalTopic = BeneficiaryUpdatedEvent.name,
//                integrationEventName = NullvsMemberBatchRequestEvent.name,
//            )
//
//            val batchRequest = buildNullvsMemberBatchRequest(
//                person, pendingMember, metadata, NullvsActionType.UPDATE
//            )
//
//            coEvery { memberService.get(beneficiary.memberId) } returns pendingMember
//
//            val result = consumer.updateBeneficiaryTotvs(event)
//
//            ResultAssert.assertThat(result).isSuccess()
//            ResultAssert.assertThat(result).isSuccessWithData(false)
//
//            coVerifyNone { totvsMemberIntegrationService.updateBeneficiaryTotvs(any(), any()) }
//            coVerifyNone {
//                kafkaProducerService.produce(
//                    NullvsMemberBatchRequestEvent(batchRequest),
//                    beneficiary.personId.toString()
//                )
//            }
//
//        }
//
//        @Test
//        fun `#updateBeneficiaryTotvs should return error when not founding a valid membership for the beneficiary`(): Unit =
//            runBlocking {
//                val event = BeneficiaryUpdatedEvent(beneficiary)
//
//                coEvery { memberService.get(beneficiary.memberId) } returns NotFoundException("Member not found").failure()
//
//                val result = consumer.updateBeneficiaryTotvs(event)
//
//                ResultAssert.assertThat(result).isFailure()
//
//                coVerifyNone { totvsMemberIntegrationService.updateBeneficiaryTotvs(any(), any()) }
//                coVerifyNone { kafkaProducerService.produce(any(), any()) }
//            }
//
//        @Test
//        fun `#updateBeneficiaryTotvs should skip DLQ on error when not founding nullvs record for the member`(): Unit =
//            runBlocking {
//                val event = BeneficiaryUpdatedEvent(beneficiary)
//
//                coEvery { memberService.get(beneficiary.memberId) } returns member
//                coEvery {
//                    totvsMemberIntegrationService.updateBeneficiaryTotvs(
//                        any(),
//                        member
//                    )
//                } returns TotvsMemberNotFoundByInternalIdException("")
//
//                val result = consumer.updateBeneficiaryTotvs(event)
//
//                ResultAssert.assertThat(result).isSuccess()
//                ResultAssert.assertThat(result).isSuccessWithData(false)
//
//                coVerifyOnce {
//                    totvsMemberIntegrationService.updateBeneficiaryTotvs(any(), any())
//                }
//                coVerifyNone {
//                    kafkaProducerService.produce(any(), any())
//                }
//            }

//    }
}
