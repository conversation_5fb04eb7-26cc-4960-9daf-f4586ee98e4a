package br.com.alice.nullvs.models.company

import java.util.UUID

data class CompanyInfo(
    val companyId: UUID,
    val cnpj: String,
    val contractId: UUID? = null,
    val contractNumber: String?,
    val subContractId: UUID? = null,
    val subcontractNumber: String?,
    val hasCompanyProductPriceListing: Boolean,
)

data class CompanyContractInfo(
    val groupCompany: String? = null,
    val contractNumber: String? = null,
    val subcontractNumber: String? = null
)
