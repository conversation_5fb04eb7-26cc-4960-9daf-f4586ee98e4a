package br.com.alice.api.ehr.routes

import io.ktor.server.routing.Routing
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    healthCheckRoutes()
    pdfRoutes()
    healthSpecialistRoute()
    clientEventRoutes()

    route("/ehr") {
        mainRoutes()
        accreditedNetworkRoutes()
        actionRoutes()
        appointmentRoutes()
        authRoutes()
        budRoutes()
        cockpitRoutes()
        counterReferralRoutes()
        healthConditionRoutes()
        healthDemandRoutes()
        healthLogicsRoutes()
        healthMeasurementRoutes()
        healthPlanTaskTemplateRoutes()
        locationRoutes()
        medicineRoutes()
        memberHubRoutes()
        pelicanRoutes()
        personRoutes()
        prescriptionRoutes()
        procedureRoutes()
        specialistRoutes()
        specialtyRoutes()
        staffRoutes()
        tertiaryIntentionRoutes()
        testRoutes()
        timelineRoutes()
        taskDetailsRoute()
    }
}
