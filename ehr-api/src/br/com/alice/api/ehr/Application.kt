package br.com.alice.api.ehr

import br.com.alice.akinator.ioc.AkinatorDomainClientModule
import br.com.alice.api.ehr.clients.ehrqa.EhrQuestionAnswerClient
import br.com.alice.api.ehr.clients.pelican.PelicanClient
import br.com.alice.api.ehr.controllers.*
import br.com.alice.api.ehr.controllers.accredited_network.AccreditedNetworkController
import br.com.alice.api.ehr.controllers.accredited_network.SimpleFilterController
import br.com.alice.api.ehr.controllers.accredited_network.SpecialityController
import br.com.alice.api.ehr.controllers.appointment.AppointmentExternalFilesController
import br.com.alice.api.ehr.controllers.goal.CockpitGoalController
import br.com.alice.api.ehr.controllers.tertiary_intention.TertiaryCounterReferralController
import br.com.alice.api.ehr.controllers.tertiary_intention.TertiaryGuiaController
import br.com.alice.api.ehr.controllers.tertiary_intention.TertiaryIntentionController
import br.com.alice.api.ehr.metrics.Metrics.registerMetrics
import br.com.alice.api.ehr.routes.apiRoutes
import br.com.alice.api.ehr.services.AuthService
import br.com.alice.api.ehr.services.PersonInformationService
import br.com.alice.api.ehr.services.appointmentSchedule.AppointmentScheduleService
import br.com.alice.api.ehr.services.appointment_autofill.AppointmentAutofillServiceNew
import br.com.alice.api.ehr.services.assistance_summary.SearchTriageService
import br.com.alice.api.ehr.services.dalya_recommendation.DalyaRecommendationService
import br.com.alice.api.ehr.services.emergency_room_attendance.CreateEmergencyRoomAttendance
import br.com.alice.api.ehr.services.emergency_room_attendance.SearchEmergencyRoomAttendance
import br.com.alice.api.ehr.services.emergency_room_attendance.UpdateEmergencyRoomAttendance
import br.com.alice.api.ehr.services.hospitalization_attendance.CreateHospitalizationAttendance
import br.com.alice.api.ehr.services.hospitalization_attendance.SearchHospitalizationAttendance
import br.com.alice.api.ehr.services.hospitalization_attendance.UpdateHospitalizationAttendance
import br.com.alice.api.ehr.services.internal.AppointmentExecutedInternalService
import br.com.alice.api.ehr.services.internal.accredited_network.AccreditedNetworkInternalService
import br.com.alice.api.ehr.services.internal.accredited_network.ProvidersInternalService
import br.com.alice.api.ehr.services.internal.community_cockpit.v2.RecentMedicalCareService
import br.com.alice.api.ehr.services.internal.member_hub.PersonsInternalService
import br.com.alice.api.ehr.services.internal.task.TaskReferralService
import br.com.alice.api.ehr.services.internal.task.TaskReferralSessionsService
import br.com.alice.api.ehr.services.internal.timeline.AppointmentTimelineInternalService
import br.com.alice.api.ehr.services.internal.timeline.AttendantsTimelineInternalService
import br.com.alice.api.ehr.services.person_health_info_summary.AiChatQuestionAnswerService
import br.com.alice.api.ehr.services.person_health_info_summary.PersonHealthInfoSummaryService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchAcuteCasesService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchChronicConditionService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchClinicalBackgroundService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchContinuousMedicationService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchHealthMeasurementService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchPrimaryAttentionAppointmentService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchSpecialistAppointmentService
import br.com.alice.api.ehr.services.person_health_info_summary.SearchTestResultService
import br.com.alice.api.ehr.services.procedure.ProcedureService
import br.com.alice.api.ehr.services.specialists.GetSpecialistsFacade
import br.com.alice.api.ehr.services.specialists.GetSpecialistsFacadeImpl
import br.com.alice.api.ehr.services.specialists.GetSpecialistsService
import br.com.alice.api.ehr.services.specialists.GetSpecialistsServiceImpl
import br.com.alice.api.ehr.services.specialists.clinical.ClinicalCommunityService
import br.com.alice.api.ehr.services.specialists.member_tier.MemberTierFacade
import br.com.alice.api.ehr.services.specialists.member_tier.MemberTierFacadeImpl
import br.com.alice.api.ehr.services.surgery_attendance.CreateSurgeryAttendance
import br.com.alice.api.ehr.services.surgery_attendance.SearchSurgeryAttendance
import br.com.alice.api.ehr.services.surgery_attendance.UpdateSurgeryAttendance
import br.com.alice.api.ehr.v2.controllers.profile.PersonTagsController
import br.com.alice.api.ehr.v2.routes.apiRoutesV2
import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.channel.ioc.ChannelDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.plugin.RateLimitPlugin
import br.com.alice.common.application.setupBffApi
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.googlemaps.ioc.GoogleMapsModule
import br.com.alice.common.headerOpenTelemetryTraceId
import br.com.alice.common.headerTraceId
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.notification.installNotificationSubscriptionAutoConfirm
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.ehr.ioc.EhrDomainClientModule
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.refund.ioc.RefundDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.secondary.attention.ioc.SecondaryAttentionDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import br.com.alice.testresult.ioc.TestResultDomainClientModule
import br.com.alice.wanda.ioc.WandaDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.routing
import java.time.Duration
import org.koin.core.module.Module
import org.koin.dsl.module
import br.com.alice.api.ehr.v2.controllers.profile.PersonController as PersonControllerV2

val HttpHeaders.birdIdToken get() = "bird-id-token"

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)


object ApplicationModule {
    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        NotificationModule,
        MembershipClientModule,
        ChannelDomainClientModule,
        EhrDomainClientModule,
        QuestionnaireDomainClientModule,
        HealthConditionDomainClientModule,
        FeatureConfigDomainClientModule,
        ProviderDomainClientModule,
        TestResultDomainClientModule,
        WandaDomainClientModule,
        HealthLogicDomainClientModule,
        AppointmentScheduleDomainClientModule,
        StaffDomainClientModule,
        PersonDomainClientModule,
        CoverageDomainClientModule,
        ExecIndicatorDomainClientModule,
        AppointmentDomainClientModule,
        HealthPlanDomainClientModule,
        ClinicalAccountDomainClientModule,
        SecondaryAttentionDomainClientModule,
        FileVaultClientModule,
        ProductDomainClientModule,
        MaraudersMapDomainClientModule,
        AkinatorDomainClientModule,
        RefundDomainClientModule,
        GoogleMapsModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single {
                DefaultHttpClient({ install(ContentNegotiation) { simpleGson() } }, timeoutInMillis = 15_000)
            }

            val cache = CacheFactory.newInstance("ehr-api-service-cache")

            // Services
            single { AuthService(get(), get()) }
            single { PersonInformationService(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
            single { AppointmentScheduleService(get(), get()) }
            single { SearchEmergencyRoomAttendance(get(), get(), get()) }
            single { CreateEmergencyRoomAttendance(get()) }
            single { UpdateEmergencyRoomAttendance(get()) }
            single { CreateHospitalizationAttendance(get()) }
            single { SearchHospitalizationAttendance(get(), get(), get(), get(), get(), get(), get(), get()) }
            single { UpdateHospitalizationAttendance(get()) }
            single { CreateSurgeryAttendance(get()) }
            single { SearchSurgeryAttendance(get(), get(), get(), get(), get(), get(), get(), get()) }
            single { ProcedureService(get()) }
            single { UpdateSurgeryAttendance(get()) }
            single { SearchTriageService(get(), get(), get(), get(), get(), get(), get()) }
            single<GetSpecialistsService> {
                GetSpecialistsServiceImpl(
                    get(),
                    get()
                )
            }
            single<GetSpecialistsFacade> { GetSpecialistsFacadeImpl(get()) }
            single<MemberTierFacade> { MemberTierFacadeImpl(get(), get(), get(), get(), get()) }
            single<AppointmentAutofillServiceNew> { AppointmentAutofillServiceNew(get(), get(), get(), get()) }
            single<DalyaRecommendationService> { DalyaRecommendationService(get(), get(), get(), get()) }
            single { SearchHealthMeasurementService(get()) }
            single { SearchTestResultService(get()) }
            single { SearchClinicalBackgroundService(get()) }
            single { SearchChronicConditionService(get(), get(), get()) }
            single { SearchContinuousMedicationService(get(), get()) }
            single { SearchSpecialistAppointmentService(get(), get(), get(), get()) }
            single { SearchPrimaryAttentionAppointmentService(get(), get(), get()) }
            single { SearchAcuteCasesService(get(), get(), get()) }
            single { AiChatQuestionAnswerService(get(), get(), get()) }
            single {
                PersonHealthInfoSummaryService(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }

            // Client
            single { DataEventClient() }
            single { EhrQuestionAnswerClient() }

            // Controllers
            single { AppointmentEventController(get()) }
            single { AppointmentMacroController(get()) }
            single { AutomaticTaskEngineController(get(), get(), get()) }
            single { AppointmentAutofillController(get(), get()) }
            single {
                AppointmentController(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single { AppointmentExternalFilesController(get(), get()) }
            single {
                AppointmentEvolutionController(
                    get(),
                    get(),
                    get(),
                )
            }
            single { AppointmentScheduleController(get(), get()) }
            single { AssistanceSummaryController(get(), get(), get()) }
            single { AuthController(get(), get(), get()) }
            single { AuthSpecialistsController(get()) }
            single { AppointmentProcedureExecutedController(get(), get()) }
            single { BudController(get(), get(), get(), get()) }
            single { CaseRecordController(get(), get()) }
            single {
                CertificateSigningController(
                    get(),
                    get(),
                )
            }
            single { ClientEventsController() }
            single { ClinicalProfileController(get(), get(), get(), get(), get(), get()) }
            single { CockpitController(get(), get(), get()) }
            single { CounterReferralController(get(), get(), get(), get(), get(), get(), get(), get()) }
            single { CounterReferralRelevanceController(get()) }
            single { CptsController(get(), get(), get()) }
            single { DischargeSummaryController(get()) }
            single {
                HealthConditionController(
                    get(),
                    get(),
                    get(),
                    get()
                )
            }
            single { HealthDemandController(get(), get(), get(), get(), get(), get(), get()) }
            single { HealthController(EHR_API_ROOT_SERVICE_NAME) }
            single { HealthFormController(get()) }
            single { HealthLogicController(get(), get(), get()) }
            single {
                HealthPlanController(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                )
            }
            single { HealthPlanTaskController(get(), get(), get()) }
            single { HealthPlanTaskTemplateController(get(), get()) }
            single { HealthMeasurementController(get(), get(), get()) }
            single { LaboratoryTestResultController(get()) }
            single { MainController(get(), get(), get(), get()) }
            single { MedicineController(get(), get()) }
            single { DocumentController(get(), get(), get()) }
            single { PersonController(get(), get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
            single { PersonCptsController(get()) }
            single { PersonTaskController(get()) }
            single { StatusController(get(), get()) }
            single { StaffController(get()) }
            single { TestCodeController(get()) }
            single { TestResultController(get(), get(), get(), get()) }
            single { TestResultFileController(get(), get()) }
            single { SpecialtyController(get(), get(), cache) }
            single { ThirdPartyAppointmentController(get(), get()) }
            single {
                PregnancyController(
                    get(),
                    get(),
                )
            }
            single {
                PrescriptionController(
                    get(),
                    get(),
                    get(),
                )
            }
            single { EmergencyRoomAttendanceController(get(), get(), get(), get()) }
            single { HospitalizationAttendanceController(get(), get(), get(), get()) }
            single { SurgeryAttendanceController(get(), get(), get(), get()) }
            single { LocationController(get()) }
            single { CommunityCockpitController(get(), get(), get(), get(), get(), get()) }
            single { HealthPlanTaskCommunityController(get(), get(), get(), get()) }
            single { SpecialistController(get(), get(), get(), cache, get(), get(), get()) }
            single { HealthcareTeamController(get(), get(), get(), get(), get()) }
            single { ProcedureController(get()) }
            single { TussProcedureController(get()) }
            single { SummaryFileController(get(), get(), get(), get(), get()) }
            single { MemberTierController(get()) }
            single { HLActionRecommendationController(get()) }
            single { TimelineFilterController(get()) }
            single { SpecialistOpinionController(get(), get(), get(), get(), get(), get(), get()) }
            single { PelicanController(get()) }
            single { PelicanController(get()) }
            single { ClinicalBackgroundController(get()) }
            single { RefundCounterReferralController(get(), get(), get()) }
            single { ClinicalCommunityService(get(), get()) }
            single { AccreditedNetworkController(get()) }
            single { SimpleFilterController() }
            single { SpecialityController(get()) }
            single { MemberHubController(get()) }
            single { MapsAddressController(get()) }
            single { TertiaryGuiaController(get(), get(), get()) }
            single { TertiaryIntentionController() }
            single { TertiaryCounterReferralController(get(), get(), get(), get()) }
            single { DalyaRecommendationController(get()) }
            single { AppointmentProcedureSelectionController(get(), get(), get()) }
            single { SuggestedProcedureController(get(), get(), get()) }
            single { PersonHealthInfoSummaryController(get(), get()) }
            single { CockpitGoalController(get(), get(), get(), get()) }
            single { AppointmentFileController(get(), get()) }
            single { TaskDetailsController(get(), get(), get()) }

            //Internal
            single { AppointmentTimelineInternalService(get(), get()) }
            single { AttendantsTimelineInternalService(get(), get()) }
            single { PelicanClient(get()) }
            single { AccreditedNetworkInternalService(get(), get(), get(), get(), get(), get()) }
            single { ProvidersInternalService(get(), get(), get(), get()) }
            single { PersonsInternalService(get(), get(), get()) }
            single { AppointmentExecutedInternalService(get(), get(), get(), get(), get()) }
            single { RecentMedicalCareService(get(), get(), get()) }
            single { TaskReferralSessionsService(get(), get()) }
            single { TaskReferralService(get(), get()) }

            // v2 - controllers
            single { PersonControllerV2(get()) }
            single { PersonTagsController(get()) }
            single {
                br.com.alice.api.ehr.controllers.v2.TimelineController(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                )
            }
            single {
                br.com.alice.api.ehr.controllers.v2.CommunityCockpitController(
                    get(), get(), get(), get(), get()
                )
            }
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupBffApi(
        dependencyInjectionModules,
        withOriginalErrorMessage = true
    ) {
        install(CORS) {
            allowMethod(HttpMethod.Get)
            allowMethod(HttpMethod.Post)
            allowMethod(HttpMethod.Put)
            allowMethod(HttpMethod.Delete)
            allowMethod(HttpMethod.Options)
            allowHeader(HttpHeaders.Authorization)
            allowHeader(HttpHeaders.ContentType)
            allowHeader(HttpHeaders.ContentRange)
            allowHeader(HttpHeaders.birdIdToken)
            allowHeader("Session-Id")
            allowHeadersPrefixed("X-Datadog-")
            allowHeader("traceparent")
            exposeHeader(HttpHeaders.ContentRange)
            exposeHeader(headerTraceId)
            exposeHeader(headerOpenTelemetryTraceId)
            exposeHeader(HttpHeaders.ContentDisposition)
            // TODO: be more restrict on hosts
            anyHost()
            maxAgeInSeconds = Duration.ofDays(7).seconds
        }

        routing {
            application.attributes.put(PolicyRootServiceKey, EHR_API_ROOT_SERVICE_NAME)
            apiRoutes()
            apiRoutesV2()
        }

        install(RateLimitPlugin)

        featureConfigBootstrap(
            FeatureNamespace.CHANNELS,
            FeatureNamespace.EHR,
            FeatureNamespace.HEALTH_LOGICS,
            FeatureNamespace.SECONDARY_ATTENTION,
            FeatureNamespace.EXEC_INDICATOR,
            FeatureNamespace.SCHEDULE,
            FeatureNamespace.WANDA
        )
        installNotificationSubscriptionAutoConfirm()
        registerMetrics()
    }
}
