package br.com.alice.common.application

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.rfcRoutes
import br.com.alice.common.extensions.setupHealthCheckRoutes
import br.com.alice.common.handleExceptions
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.metrics.setupMetrics
import br.com.alice.common.observability.opentelemetry.setupOpenTelemetry
import br.com.alice.common.ops.health.setupHealthCheckModule
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.common.service.serialization.simpleGson
import ch.qos.logback.classic.LoggerContext
import com.google.gson.FieldNamingPolicy
import io.ktor.http.ContentType
import io.ktor.serialization.gson.gson
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.ApplicationStarted
import io.ktor.server.application.Plugin
import io.ktor.server.application.uninstall
import io.ktor.server.auth.Authentication
import io.ktor.server.plugins.callloging.CallLogging
import io.ktor.server.plugins.compression.Compression
import io.ktor.server.plugins.compression.deflate
import io.ktor.server.plugins.compression.gzip
import io.ktor.server.plugins.compression.minimumSize
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.plugins.dataconversion.DataConversion
import io.ktor.server.plugins.doublereceive.DoubleReceive
import io.ktor.server.request.path
import io.ktor.server.routing.IgnoreTrailingSlash
import io.ktor.server.routing.routing
import io.ktor.util.pipeline.Pipeline
import org.koin.core.context.KoinContextHandler
import org.koin.core.context.loadKoinModules
import org.koin.core.context.unloadKoinModules
import org.koin.core.module.Module
import org.koin.ktor.plugin.Koin
import org.slf4j.LoggerFactory
import java.util.Timer
import kotlin.concurrent.schedule
import io.ktor.server.application.install as ktorInstall


internal val appBffMillisecondsMeasure: List<Long> = listOf(700L, 1500L)
internal val appDomainServiceMillisecondsMeasure: List<Long> = listOf(100L, 300L)

class Configuration {

    fun <P : Pipeline<*, ApplicationCall>, B : Any, F : Any> P.install(
        feature: Plugin<P, B, F>,
        configure: B.() -> Unit = {}
    ): F {
        uninstall(feature) // uninstall base feature from `setupDomainService` or `setupBffApi`
        uninstall(feature) // uninstall default feature from ktor https://github.com/ktorio/ktor/issues/1887#issuecomment-*********

        return ktorInstall(feature, configure)
    }
}

fun withDefaultModules(dependencyInjectionModules: List<Module>) = dependencyInjectionModules + setupHealthCheckModule()

// TODO: Remove after migrate clients to `setupDomainService` or `setupBffApi`
@Deprecated("Use `setupDomainService` or `setupBffApi` instead")
fun Application.setup(
    dependencyInjectionModules: List<Module>,
    measureMillisecondsThreshold: List<Long>? = null,
    configure: Configuration.() -> Unit = {},
) {
    val diModules = withDefaultModules(dependencyInjectionModules)

    // Check for duplicate resources in classpath early in the startup process
    checkForDuplicateResources()

    environment.monitor.subscribe(ApplicationStarted) {
        Timer().schedule(3_000) {
            applicationStarted(it, BaseConfig.instance.runningMode)
        }
        scheduleLogbackStatusClear()
    }

    ktorInstall(Compression) {
        gzip {
            priority = 1.0
        }
        deflate {
            priority = 10.0
            minimumSize(1024) // condition
        }
    }

    ktorInstall(CallLogging) {
        filter { call ->
            !call.request.path().startsWith("/ops")
        }
    }
    ktorInstall(DoubleReceive)

    val koin = KoinContextHandler.getOrNull()

    if (koin != null) {
        unloadKoinModules(diModules)
        loadKoinModules(diModules)
    } else {
        ktorInstall(Koin) {
            modules(diModules)
        }
    }

    ktorInstall(DataConversion)
    ktorInstall(IgnoreTrailingSlash)

    ktorInstall(ContentNegotiation) {
        gsonSnakeCase()

        gson(ContentType.Text.Plain) // this is necessary to receive SNS events
    }

    handleExceptions()

    routing {
        setupHealthCheckRoutes()
    }

    setupOpenTelemetry()
    setupMetrics(measureMillisecondsThreshold)

    configure(Configuration())
}

fun Application.setupBffApi(
    dependencyInjectionModules: List<Module>,
    fieldNamingPolicy: FieldNamingPolicy = FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES,
    contentType: ContentType = ContentType.Text.Plain,
    withOriginalErrorMessage: Boolean = false,
    measureMillisecondsThreshold: List<Long>? = null,
    configure: Configuration.() -> Unit = {},
) {
    setup(
        dependencyInjectionModules,
        withOriginalErrorMessage,
        measureMillisecondsThreshold ?: appBffMillisecondsMeasure
    )

    ktorInstall(ContentNegotiation) {
        simpleGson {
            setFieldNamingPolicy(fieldNamingPolicy)
        }

        gson(contentType) // this is necessary to receive SNS events
    }

    routing {
        setupHealthCheckRoutes()
    }

    ktorInstall(Authentication) {
        <EMAIL>()
        firebase()
    }

    configure(Configuration())
}

fun Application.setupDomainService(
    dependencyInjectionModules: List<Module>,
    checkHealthWithAuth: Boolean = true,
    withOriginalErrorMessage: Boolean = true,
    measureMillisecondsThreshold: List<Long>? = null,
    configure: Configuration.() -> Unit = {}
) {
    setup(
        dependencyInjectionModules,
        withOriginalErrorMessage,
        measureMillisecondsThreshold ?: appDomainServiceMillisecondsMeasure
    )

    ktorInstall(ContentNegotiation) {
        gsonSnakeCase()

        gson(ContentType.Text.Plain) // this is necessary to receive SNS events
    }

    routing {
        setupHealthCheckRoutes()
        rfcRoutes(checkHealthWithAuth = checkHealthWithAuth)
    }

    configure(Configuration())
}

private fun Application.setup(
    dependencyInjectionModules: List<Module>,
    withOriginalErrorMessage: Boolean = false,
    measureMillisecondsThreshold: List<Long>? = null,
) {
    val diModules = withDefaultModules(dependencyInjectionModules)

    // Check for duplicate resources in classpath early in the startup process
    checkForDuplicateResources()

    environment.monitor.subscribe(ApplicationStarted) {
        Timer().schedule(3_000) {
            applicationStarted(it, BaseConfig.instance.runningMode)
        }
        scheduleLogbackStatusClear()
    }

    ktorInstall(Compression) {
        gzip {
            priority = 1.0
        }
        deflate {
            priority = 10.0
            minimumSize(1024) // condition
        }
    }

    ktorInstall(CallLogging) {
        filter { call ->
            !call.request.path().startsWith("/ops")
        }
    }
    ktorInstall(DoubleReceive)

    ktorInstall(Koin) {
        modules(diModules)
    }

    ktorInstall(DataConversion)
    ktorInstall(IgnoreTrailingSlash)

    handleExceptions(withOriginalErrorMessage)

    setupOpenTelemetry()
    setupMetrics(measureMillisecondsThreshold)
}

private const val LOGBACK_CLEAR_PERIOD = 60_000 * 10L //every 10 minutes

private fun scheduleLogbackStatusClear() {
    Timer().schedule(LOGBACK_CLEAR_PERIOD, LOGBACK_CLEAR_PERIOD) {
        val context = LoggerFactory.getILoggerFactory() as LoggerContext
        context.statusManager.copyOfStatusList.forEach {
            logger.warn("Dumping status message",
                "log_message" to it.message,
                "log_level" to it.level,
                "log_throwable" to it.throwable?.message,
            )
        }
        context.statusManager.clear()
    }
}

private fun checkForDuplicateResources() {
    try {
        DuplicateResourceChecker().checkForDuplicates()
    } catch (e: DuplicateResourceException) {
        logger.error("Application startup failed due to duplicate resources in classpath", e)
        throw e
    }
}
