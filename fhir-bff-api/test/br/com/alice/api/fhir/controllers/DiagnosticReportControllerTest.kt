package br.com.alice.api.fhir.controllers

import br.com.alice.api.fhir.ControllerTestHelper
import br.com.alice.api.fhir.FhirBffApiConfiguration
import br.com.alice.api.fhir.controllers.exceptions.PersonNotFound
import br.com.alice.api.fhir.logics.OperationOutcomeLogic
import br.com.alice.api.fhir.metrics.DiagnosticReportMetric
import br.com.alice.api.fhir.metrics.MetricStatus.FAILURE
import br.com.alice.api.fhir.metrics.MetricStatus.SUCCESS
import br.com.alice.api.fhir.models.OperationOutcome
import br.com.alice.api.fhir.services.AuthService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.readFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FhirProviderAccess
import br.com.alice.data.layer.models.FhirVersion.R4
import br.com.alice.data.layer.models.ProviderIntegration.DASA
import br.com.alice.fhir.client.FhirDocumentService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.runs
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.hl7.fhir.r4.model.DiagnosticReport
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class DiagnosticReportControllerTest : ControllerTestHelper() {

    private val personService: PersonService = mockk()
    private val authService: AuthService = mockk()
    private val fhirDocumentService: FhirDocumentService = mockk()
    private val diagnosticReportController = DiagnosticReportController(
        authService,
        personService,
        fhirDocumentService
    )

    private val defaultHeaders = mapOf(HttpHeaders.Accept to "application/json;fhirVersion=4.0")

    private val dasaRequestJson = readFile("testResources/DasaDiagnosticReport.json")
    private val payloadWithoutNationalId = readFile("testResources/DiagnosticReportWithoutNationalId.json")
    private val identifierId = "533701801614"
    private val reportId = "d704a146aebc58dafc53e31a"
    private val nationalId = "01234567890"
    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId, nationalId = nationalId)
    private val fhirDocument = TestModelFactory.buildFhirDocument(personId = personId)
    private val providerAccess = FhirProviderAccess(provider = DASA)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { diagnosticReportController }
        mockkObject(DiagnosticReportMetric)
        every { DiagnosticReportMetric.registerCounter() } just runs
    }

    @AfterTest
    fun confirmMocks() {
        verifyOnce { DiagnosticReportMetric.registerCounter() }
        confirmVerified(
            personService,
            authService,
            fhirDocumentService,
            DiagnosticReportMetric
        )
    }

    @Test
    fun `#process - should not process diagnostic if missing member nationalId`() =
        mockRangeUUID { uuid ->
            val expected = OperationOutcomeLogic
                .buildOperationOutcome(
                    InvalidArgumentException(
                        code = "national_id_null",
                        message = "nationalId is null"
                    )
                ).copy(id = uuid.toString())

            coEvery { authService.getProviderName(token) } returns providerAccess.success()
            every { DiagnosticReportMetric.increment(FAILURE, DASA) } returns true

            authenticated(token) {
                post("/DiagnosticReport", payloadWithoutNationalId, defaultHeaders) { response ->
                    assertThat(response).isUnprocessableEntity()

                    val result = gson.fromJson<OperationOutcome>(response.bodyAsText())
                    assertThat(result).isEqualTo(expected)
                }
            }

            coVerifyOnce { authService.getProviderName(any()) }
            verifyOnce { DiagnosticReportMetric.increment(any(), any()) }
        }

    @Test
    fun `#process - should not process diagnostic report if personNotFound`() =
        mockRangeUUID { uuid ->
            val expected = OperationOutcomeLogic
                .buildOperationOutcome(PersonNotFound())
                .copy(id = uuid.toString())

            coEvery { authService.getProviderName(token) } returns providerAccess.success()
            coEvery { personService.findByNationalId(nationalId) } returns NotFoundException("resource_not_found").failure()
            every { DiagnosticReportMetric.increment(FAILURE, DASA) } returns true

            authenticated(token) {
                post("/DiagnosticReport", dasaRequestJson, defaultHeaders) { response ->

                    assertThat(response).isUnprocessableEntity()
                    assertThat(gson.fromJson<OperationOutcome>(response.bodyAsText())).isEqualTo(expected)
                }
            }

            coVerifyOnce { authService.getProviderName(any()) }
            coVerifyOnce { personService.findByNationalId(any()) }
            verifyOnce { DiagnosticReportMetric.increment(any(), any()) }
        }

    @Test
    fun `#process - should throw an internal server error (500) when the error is unexpected`() =
        mockRangeUUID { uuid ->
            val expected = OperationOutcomeLogic
                .buildOperationOutcome("unexpected_error")
                .copy(id = uuid.toString())

            coEvery { authService.getProviderName(token) } returns providerAccess.success()
            coEvery { personService.findByNationalId(nationalId) } returns person.success()
            coEvery {
                fhirDocumentService.upsert(
                    personId = personId,
                    providerIntegration = DASA,
                    externalId = identifierId,
                    fhirResource = DiagnosticReport::class.simpleName.toString(),
                    fhirVersion = R4,
                    requestJson = dasaRequestJson
                )
            } returns Exception().failure()
            every { DiagnosticReportMetric.increment(FAILURE, DASA) } returns true

            authenticated(token) {
                post("/DiagnosticReport", dasaRequestJson, defaultHeaders) { response ->
                    assertThat(response).isInternalServerError()

                    val result = gson.fromJson<OperationOutcome>(response.bodyAsText())
                    assertThat(result)
                        .usingRecursiveComparison()
                        .ignoringFields("issue.details")
                        .isEqualTo(expected)
                }
            }

            coVerifyOnce { authService.getProviderName(any()) }
            coVerifyOnce { personService.findByNationalId(any()) }
            coVerifyOnce { fhirDocumentService.upsert(any(), any(), any(), any(), any(), any()) }
            verifyOnce { DiagnosticReportMetric.increment(any(), any()) }
        }

    @Test
    fun `#process - should process dasa diagnostic report`() {
        val expectedHeader = "${FhirBffApiConfiguration.baseUrl()}/DiagnosticReport/$reportId"

        coEvery { authService.getProviderName(token) } returns providerAccess.success()
        coEvery { personService.findByNationalId(nationalId) } returns person.success()
        coEvery {
            fhirDocumentService.upsert(
                personId = personId,
                providerIntegration = DASA,
                externalId = identifierId,
                fhirResource = DiagnosticReport::class.simpleName.toString(),
                fhirVersion = R4,
                requestJson = dasaRequestJson
            )
        } returns fhirDocument.success()
        every { DiagnosticReportMetric.increment(SUCCESS, DASA) } returns true

        authenticated(token) {
            post("/DiagnosticReport", dasaRequestJson, defaultHeaders) { response ->
                assertThat(response).isCreated()
                assertThat(response).containsHeaderWithValue(HttpHeaders.Location, expectedHeader)
            }
        }

        coVerifyOnce { authService.getProviderName(any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerifyOnce { fhirDocumentService.upsert(any(), any(), any(), any(), any(), any()) }
        verifyOnce { DiagnosticReportMetric.increment(any(), any()) }
    }

    @Test
    fun `#process - should not integrate exam the of person`() = runBlocking {
        coEvery { authService.getProviderName(token) } returns providerAccess.success()
        coEvery { personService.findByNationalId(nationalId) } returns person.success()

        withFeatureFlag(FeatureNamespace.FHIR, "should_not_integrate_exam_by_person", listOf(personId.toString())) {
            authenticated(token) {
                post("/DiagnosticReport", dasaRequestJson, defaultHeaders) { response ->
                    assertThat(response).isOK()
                }
            }
        }

        coVerifyOnce { authService.getProviderName(any()) }
        coVerifyOnce { personService.findByNationalId(any()) }
        coVerify { fhirDocumentService wasNot called }
    }
}
