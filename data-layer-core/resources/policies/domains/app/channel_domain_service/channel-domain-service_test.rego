package app.channel_domain_service_test

import rego.v1

import data.app.channel_domain_service

test_unauth_view_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "AliceAgoraWorkingHours"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Appointment"
                },
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "DeviceModel"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareAdditionalTeam"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthForm"
                },
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberModel"
                },
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonCase"
                },
            },
            {
                "index": 8,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                },
            }
        ]
    }
}

test_unauth_view_and_count_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BillingAccountablePartyModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BillingAccountablePartyModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CaseRecord"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CaseRecord"
                }
            },            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CassiMemberModel"
                }
            },
            {
                "index": 12,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CassiMemberModel"
                }
            },
            {
                "index": 13,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 14,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 15,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 16,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanyContractModel"
                }
            },
            {
                "index": 17,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            },
            {
                "index": 18,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "CompanySubContractModel"
                }
            },
            {
                "index": 19,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            },
            {
                "index": 20,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "GenericFileVault"
                }
            },
            {
                "index": 21,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 22,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 23,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthCondition"
                }
            },
            {
                "index": 24,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthCondition"
                }
            },
            {
                "index": 25,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionAxis"
                }
            },
            {
                "index": 26,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionAxis"
                }
            },
            {
                "index": 27,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionRelated"
                }
            },
            {
                "index": 28,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionRelated"
                }
            },            {
                "index": 29,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionTemplate"
                }
            },
            {
                "index": 30,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthConditionTemplate"
                }
            },
            {
                "index": 31,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestion"
                }
            },
            {
                "index": 32,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestion"
                }
            },
            {
                "index": 33,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormSection"
                }
            },
            {
                "index": 34,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormSection"
                }
            },
            {
                "index": 35,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 36,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 37,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 38,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 39,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Risk"
                }
            },
            {
                "index": 40,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Risk"
                }
            },
            {
                "index": 41,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "RiskCalculationConf"
                }
            },
            {
                "index": 42,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "RiskCalculationConf"
                }
            },
            {
                "index": 43,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 44,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            }
        ]
    }
}

test_unauth_view_and_create_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormAnswerGroup"
                },
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormAnswerGroup"
                },
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                },
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StaffChannelHistory"
                },
            },
            {
                "index": 6,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "StaffChannelHistory"
                },
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                },
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                },
            }
        ]
    }
}

test_unauth_view_and_update_models_allowed if {
    { 1, 2} == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FileVault"
                },
            },
            {
                "index": 2,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FileVault"
                },
            }
        ]
    }
}

test_unauth_view_and_create_and_update_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FollowUpHistory"
                },
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FollowUpHistory"
                },
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FollowUpHistory"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoiceItemModel"
                },
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoiceItemModel"
                },
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoiceItemModel"
                },
            },
             {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoicePaymentModel"
                },
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoicePaymentModel"
                },
            },
            {
                "index": 9,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "InvoicePaymentModel"
                },
            },
            {
                "index": 10,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceGroupModel"
                },
            },
            {
                "index": 11,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceGroupModel"
                },
            },
            {
                "index": 12,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceGroupModel"
                },
            },
            {
                "index": 13,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceModel"
                },
            },
            {
                "index": 14,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceModel"
                },
            },
            {
                "index": 15,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberInvoiceModel"
                },
            },
            {
                "index": 16,
                "action": "view",
                "subject": {
                  "opaType": "Unauthenticated"
                },
                "resource": {
                  "opaType": "BolepixPaymentDetailModel"
                },
            },
            {
                "index": 17,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BolepixPaymentDetailModel"
                },
            },
            {
                "index": 18,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BolepixPaymentDetailModel"
                },
            },
            {
                "index": 19,
                "action": "view",
                "subject": {
                  "opaType": "Unauthenticated"
                },
                "resource": {
                  "opaType": "VideoCall"
                },
            },
            {
                "index": 20,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "VideoCall"
                },
            },
            {
                "index": 21,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "VideoCall"
                },
            },
            {
                "index": 22,
                "action": "view",
                "subject": {
                  "opaType": "Unauthenticated"
                },
                "resource": {
                  "opaType": "Channel"
                },
            },
            {
                "index": 23,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Channel"
                },
            },
            {
                "index": 24,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "Channel"
                },
            },
            {
                "index": 25,
                "action": "view",
                "subject": {
                  "opaType": "Unauthenticated"
                },
                "resource": {
                  "opaType": "BoletoPaymentDetailModel"
                },
            },
            {
                "index": 26,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BoletoPaymentDetailModel"
                },
            },
            {
                "index": 27,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BoletoPaymentDetailModel"
                },
            },
            {
                "index": 28,
                "action": "view",
                "subject": {
                  "opaType": "Unauthenticated"
                },
                "resource": {
                  "opaType": "PixPaymentDetailModel"
                },
            },
            {
                "index": 29,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PixPaymentDetailModel"
                },
            },
            {
                "index": 30,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PixPaymentDetailModel"
                },
            }
        ]
    }
}

test_unauth_view_and_count_and_update_models_allowed if {
    { 1, 2, 3} == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                },
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                },
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                },
            }
        ]
    }
}

test_unauth_view_and_count_and_create_models_allowed if {
    { 1, 2, 3, 4, 5, 6 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "AppointmentEvolution"
                },
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "AppointmentEvolution"
                },
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "AppointmentEvolution"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                },
            },
            {
                "index": 5,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                },
            },
            {
                "index": 6,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                },
            }
        ]
    }
}

test_unauth_view_and_count_and_create_and_update_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelFup"
                },
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelFup"
                },
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelFup"
                },
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelFup"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestionAnswer"
                },
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestionAnswer"
                },
            },
            {
                "index": 7,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestionAnswer"
                },
            },
            {
                "index": 8,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthFormQuestionAnswer"
                },
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
            {
                "index": 10,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
            {
                "index": 11,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
            {
                "index": 12,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
        ]
    }
}

test_staff_subject_and_role_alice_health_professional_view_models_allowed if {
    { 1 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                },
            }
        ]
    }
}

test_staff_subject_and_role_alice_health_professional_view_and_create_and_update_models_allowed if {
    { 1, 2, 3 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                },
            }
        ]
    }
}


test_person_subject_view_and_create_and_update_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 } == channel_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 4,
                "action": "create",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 5,
                "action": "update",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "ChannelHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 8,
                "action": "view",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 9,
                "action": "create",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 10,
                "action": "create",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 11,
                "action": "update",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "person-id"
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            },
            {
                "index": 12,
                "action": "update",
                "subject": {
                    "opaType": "PersonSubject",
                    "id": "antother-id",
                    "dependentPersons": ["person-id","another-id"]
                },
                "resource": {
                    "opaType": "FollowUpHistory",
                    "personId": "person-id"
                },
            }
        ]
    }
}
