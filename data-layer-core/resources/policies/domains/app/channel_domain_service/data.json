{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["AliceAgoraWorkingHours", "Appointment", "DeviceModel", "HealthcareAdditionalTeam", "HealthForm", "MemberModel", "PersonCase", "PersonInternalReference"], "actions": ["view"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "BillingAccountablePartyModel", "CaseRecord", "CassiMemberModel", "CompanyModel", "CompanyContractModel", "CompanySubContractModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "HealthcareTeamModel", "HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "HealthFormQuestion", "HealthFormSection", "PersonClinicalAccount", "PersonModel", "Risk", "RiskCalculationConf", "StaffModel"], "actions": ["view", "count"]}, {"resources": ["HealthFormAnswerGroup", "PersonHealthEvent", "StaffChannelHistory", "TrackPersonABModel"], "actions": ["view", "create"]}, {"resources": ["FileVault"], "actions": ["view", "update"]}, {"resources": ["Channel", "FollowUpHistory", "InvoiceItemModel", "InvoicePaymentModel", "ResourceSignTokenModel", "ItauPaymentModel", "CancelPaymentOnAcquirerScheduleModel", "MemberInvoiceGroupModel", "MemberInvoiceModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "VideoCall"], "actions": ["view", "create", "update"]}, {"resources": ["HealthProfessionalModel"], "actions": ["view", "count", "update"]}, {"resources": ["AppointmentEvolution", "ChannelTheme"], "actions": ["view", "count", "create"]}, {"resources": ["ChannelFup", "ChannelHistory", "HealthFormQuestionAnswer"], "actions": ["view", "count", "create", "update"]}]}, {"conditions": ["${subject.opaType} == StaffModel", "${subject.role} @= ALICE_HEALTH_PROFESSIONAL_OR_NAVIGATOR"], "allow": [{"resources": ["PersonHealthEvent"], "actions": ["view"]}, {"resources": ["ChannelHistory"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${subject.opaType} == PersonSubject", "${subject.id} == ${resource.personId} || ${resource.personId} in ${subject.dependentPersons}"], "allow": [{"resources": ["ChannelHistory", "FollowUpHistory"], "actions": ["view", "create", "update"]}]}]}