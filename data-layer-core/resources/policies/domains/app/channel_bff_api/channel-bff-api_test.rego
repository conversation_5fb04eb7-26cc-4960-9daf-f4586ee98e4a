package app.channel_bff_api_test

import rego.v1
import data.app.channel_bff_api

test_staff_subject_view_channel_tag_allowed if {
	1 in channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelTag"
                }
            }
        ]
    }
}

test_staff_subject_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11,12} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "CassiMemberModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "CassiMemberModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "CompanyModel"
                }
            },
            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelFup"
                }
            },
            {
                "index": 12,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelFup"
                }
            }
        ]
    }
}

test_staff_subject_view_count_and_create_channel_theme_allowed if {
	{1,2,3} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                }
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel"
                },
                "resource": {
                    "opaType": "ChannelTheme"
                }
            }
        ]
    }
}

test_staff_subject_and_product_tech_role_view_and_count_resources_allowed if {
	{1,2,3,4} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            }
        ]
    }
}

test_staff_subject_and_product_tech_role_view_resources_allowed if {
	{1,2,3} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "Channel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "RoutingHistory"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {
                    "opaType": "ChannelHistory"
                }
            }
        ]
    }
}

test_staff_subject_and_alice_health_professional_or_navigator_role_view_create_and_update_resources_allowed if {
	{1,2,3,4,5,6} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NUTRITIONIST"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PHYSICAL_EDUCATOR"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "MANAGER_PSYCHOLOGIST"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            }
        ]
    }
}

test_staff_subject_and_alice_health_professional_or_navigator_role_view_count_and_update_health_professional_model_allowed if {
	{1,2,3} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NUTRITIONIST"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_staff_subject_and_alice_health_professional_or_navigator_role_view_and_create_track_person_ab_model_allowed if {
	{1,2} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                }
            }
        ]
    }
}

test_staff_subject_and_alice_health_professional_or_navigator_role_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11,12} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "ProductModel"
                }
            },
            {
                "index": 12,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ProductModel"
                }
            }
        ]
    }
}

test_staff_subject_and_alice_health_professional_or_navigator_role_view_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10,11} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfoModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "CaseRecord"
                }
            },
            {
                "index": 8,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "PersonCase"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "Channel"
                }
            },
            {
                "index": 10,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "NAVIGATOR"
                },
                "resource": {
                    "opaType": "ChannelsZendeskTag"
                }
            },
            {
                "index": 11,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CHIEF_PHYSICIAN"
                },
                "resource": {
                    "opaType": "ZendeskExternalReference"
                }
            }
        ]
    }
}

test_staff_subject_and_cx_ops_role_view_member_model_allowed if {
	1 in channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "CX_OPS"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            }
        ]
    }
}

test_staff_subject_and_technique_nurse_role_view_resources_allowed if {
	{1,2,3,4,5,6} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            }
        ]
    }
}

test_staff_subject_and_technique_nurse_role_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            }
        ]
    }
}

test_staff_subject_and_technique_nurse_role_view_create_and_update_resources_allowed if {
	{1,2,3,4,5,6,7,8,9} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 9,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "TECHNIQUE_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_staff_subject_and_health_ops_lead_role_view_resources_allowed if {
	{1,2,3,4,5,6,7} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "Channel"
                }
            }
        ]
    }
}

test_staff_subject_and_health_ops_lead_role_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            }
        ]
    }
}

test_staff_subject_and_health_ops_lead_role_view_create_and_update_resources_allowed if {
	{1,2,3,4,5,6,7,8,9} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 9,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "HEALTH_OPS_LEAD"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_staff_subject_and_risk_nurse_role_view_resources_allowed if {
	{1,2,3,4,5,6} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            }
        ]
    }
}

test_staff_subject_and_risk_nurse_role_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8,9,10} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ChannelMacro"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 9,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 10,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            }
        ]
    }
}

test_staff_subject_and_risk_nurse_role_view_create_and_update_resources_allowed if {
	{1,2,3,4,5,6,7,8,9} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "ChannelComment"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 5,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 6,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 8,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            },
            {
                "index": 9,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "HealthProfessionalModel"
                }
            }
        ]
    }
}

test_staff_test_subject_and_risk_nurse_role_view_resources_allowed if {
	{1,2,3,4} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "MemberModel",
                    "isTest": true
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonInternalReference",
                    "isTest": true
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo",
                    "isTest": true
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel",
                    "isTest": true
                }
            }
        ]
    }
}

test_staff_subject_and_risk_intermittent_nurse_role_view_resources_allowed if {
	{1,2,3,4,5,6} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "MemberModel"
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonHealthEvent"
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PregnancyModel"
                }
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "ClinicalBackground"
                }
            }
        ]
    }
}

test_staff_subject_and_risk_intermittent_nurse_role_view_and_count_resources_allowed if {
	{1,2,3,4,5,6,7,8} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel"
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "StaffModel"
                }
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 6,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                }
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            },
            {
                "index": 8,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                }
            }
        ]
    }
}

test_staff_subject_and_risk_intermittent_nurse_role_view_create_and_update_resources_allowed if {
	{1,2,3} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "VideoCall"
                }
            }
        ]
    }
}

test_staff_test_subject_and_risk_intermittent_nurse_role_view_resources_allowed if {
	{1,2,3,4} == channel_bff_api.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "MemberModel",
                    "isTest": true
                }
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonInternalReference",
                    "isTest": true
                }
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonAdditionalInfo",
                    "isTest": true
                }
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "RISK_INTERMITTENT_NURSE"
                },
                "resource": {
                    "opaType": "PersonModel",
                    "isTest": true
                }
            }
        ]
    }
}
