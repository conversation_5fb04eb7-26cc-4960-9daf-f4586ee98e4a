{"rules": [{"conditions": ["${subject.opaType} == StaffModel"], "allow": [{"resources": ["ChannelTag"], "actions": ["view"]}, {"resources": ["Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "CassiMemberModel", "CompanyModel", "ChannelFup"], "actions": ["view", "count"]}, {"resources": ["ChannelTheme"], "actions": ["view", "count", "create"]}], "branches": [{"conditions": ["${subject.role} @= PRODUCT_TECH"], "allow": [{"resources": ["StaffModel", "ChannelMacro"], "actions": ["view", "count"]}, {"resources": ["Channel", "RoutingHistory", "ChannelHistory"], "actions": ["view"]}]}, {"conditions": ["${subject.role} @= ALICE_HEALTH_PROFESSIONAL_OR_NAVIGATOR"], "allow": [{"resources": ["ChannelComment", "VideoCall"], "actions": ["view", "create", "update"]}, {"resources": ["HealthProfessionalModel"], "actions": ["view", "count", "update"]}, {"resources": ["TrackPersonABModel"], "actions": ["view", "create"]}, {"resources": ["ChannelMacro", "HealthcareTeamModel", "PersonModel", "PersonClinicalAccount", "StaffModel", "ProductModel"], "actions": ["view", "count"]}, {"resources": ["MemberModel", "PersonAdditionalInfoModel", "PersonHealthEvent", "PersonInternalReference", "PregnancyModel", "ClinicalBackground", "CaseRecord", "PersonCase", "Channel", "ChannelsZendeskTag", "ZendeskExternalReference"], "actions": ["view"]}]}, {"conditions": ["${subject.role} == CX_OPS"], "allow": [{"resources": ["MemberModel"], "actions": ["view"]}]}, {"conditions": ["${subject.role} == TECHNIQUE_NURSE || ${subject.role} == HEALTH_OPS_LEAD || ${subject.role} == RISK_NURSE"], "allow": [{"resources": ["MemberModel", "PersonAdditionalInfo", "PersonHealthEvent", "PersonInternalReference", "PregnancyModel", "ClinicalBackground"], "actions": ["view"]}, {"resources": ["PersonModel", "StaffModel", "ChannelMacro", "HealthcareTeamModel", "PersonClinicalAccount"], "actions": ["view", "count"]}, {"resources": ["ChannelComment", "VideoCall", "HealthProfessionalModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${subject.role} == HEALTH_OPS_LEAD"], "allow": [{"resources": ["Channel"], "actions": ["view"]}]}, {"conditions": ["${subject.role} == RISK_NURSE && ${resource.isTest == true}"], "allow": [{"resources": ["MemberModel", "PersonInternalReference", "PersonAdditionalInfo", "PersonModel"], "actions": ["view"]}]}, {"conditions": ["${subject.role} == RISK_INTERMITTENT_NURSE"], "allow": [{"resources": ["MemberModel", "PersonAdditionalInfo", "PersonHealthEvent", "PersonInternalReference", "PregnancyModel", "ClinicalBackground"], "actions": ["view"]}, {"resources": ["PersonModel", "StaffModel", "HealthcareTeamModel", "PersonClinicalAccount"], "actions": ["view", "count"]}, {"resources": ["VideoCall"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.isTest == true}"], "allow": [{"resources": ["MemberModel", "PersonInternalReference", "PersonAdditionalInfo", "PersonModel"], "actions": ["view"]}]}]}]}]}