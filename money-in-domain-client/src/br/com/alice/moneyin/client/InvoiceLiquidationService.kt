package br.com.alice.moneyin.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.BusinessType
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.MemberInvoiceGroup
import com.github.kittinunf.result.Result
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface InvoiceLiquidationService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "invoice_liquidation"

    suspend fun add(invoiceLiquidation: InvoiceLiquidation): Result<InvoiceLiquidation, Throwable>

    suspend fun get(id: UUID): Result<InvoiceLiquidation, Throwable>

    suspend fun update(model: InvoiceLiquidation): Result<InvoiceLiquidation, Throwable>

    suspend fun create(payload: CreateInvoiceLiquidationPayload): Result<InvoiceLiquidation, Throwable>

    suspend fun cancel(id: UUID): Result<InvoiceLiquidation, Throwable>

    suspend fun markAsPaid(
        invoiceLiquidation: InvoiceLiquidation,
        invoicePayment: InvoicePayment
    ): Result<InvoiceLiquidation, Throwable>

    suspend fun payMemberInvoiceGroups(invoiceLiquidations: List<InvoiceLiquidation>): Result<Boolean, Throwable>

    suspend fun listByMemberInvoiceGroupIds(ids: List<UUID>): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun listByExternalIds(externalIds: List<String>): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun listByPersonId(personId: PersonId): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun listByCompanyId(companyId: UUID): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun listByIds(liquidationIds: List<UUID>): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun restoreMemberInvoiceGroups(invoiceLiquidation: InvoiceLiquidation): Result<List<MemberInvoiceGroup>, Throwable>

    suspend fun listInvoicesNearOverdue(
        nearOverdueDate: LocalDate
    ): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun getByBillingAccountablePartyId(billingAccountablePartyId: UUID): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun getBySubContractId(subContractId: UUID): Result<List<InvoiceLiquidation>, Throwable>

    suspend fun getByBillingAccountableAndDueDateAndNotCanceled(
        billingAccountablePartyId: UUID,
        dueDate: LocalDate
    ): Result<List<InvoiceLiquidation>, Throwable>

    data class CreateInvoiceLiquidationPayload(
        val externalId: String,
        val amount: BigDecimal,
        val addition: BigDecimal = BigDecimal.ZERO,
        val discount: BigDecimal = BigDecimal.ZERO,
        val dueDate: LocalDate,
        val memberInvoiceGroupIds: List<UUID>,
        val billingAccountablePartyId: UUID,
        val companyIds: List<UUID>,
        val subcontractIds: List<UUID>,
        val companyId: UUID?,
        val subcontractId: UUID?,
        val installment: Int,
        val totalInstallments: Int,
        val reason: PaymentReason,
        val origin: InvoicePaymentOrigin = InvoicePaymentOrigin.UNDEFINED,
        val businessType: BusinessType,
    )
}
