systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

k8sEnv = "false"
k8sEnv = ${?K8S_ENV}

ddCustomMetrics = "false"
ddCustomMetrics = ${?DD_EXPORT_CUSTOM_METRICS_ENABLED}

jaegerEndpoint = "localhost:14250"
jaegerEndpoint = ${?JAEGER_ENDPOINT}

aws {
    account = ""
    account = ${?AWS_ACCOUNT}

    region = ""
    region = ${?AWS_DEFAULT_REGION}
}

environment = "prod"
environment = ${?ENVIRONMENT}

auth {
    firebaseTokenJson = ""
    firebaseTokenJson = ${?FIREBASE_TOKEN_JSON}
    firebaseClientX509CertUrl = "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-jglxc%40alice-292f2.iam.gserviceaccount.com"
    firebaseClientX509CertUrl = ${?FIREBASE_CLIENT_X509_CERT_URL}
}

development {
    localhost = "host.docker.internal"
    localhost = ${?LOCALHOST}
    fileVaultUrl = "http://"${development.localhost}":8095"
    dataEventApiUrl = "https://data-events.staging.datalake.alice.tools"
    dataEventApiUrl = ${?DATA_EVENT_API_URL}
}

test {
    fileVaultUrl = "http://localhost:9000"
    dataEventApiUrl = "https://data-events.staging.datalake.alice.tools"
}

production {
    fileVaultHost = ${?FILE_VAULT_SERVICE_HOST}
    fileVaultUrl = "https://"${?production.fileVaultHost}
    dataEventApiUrl = "https://data-events.datalake.alice.tools"
    dataEventApiUrl = ${?DATA_EVENT_API_URL}
}
