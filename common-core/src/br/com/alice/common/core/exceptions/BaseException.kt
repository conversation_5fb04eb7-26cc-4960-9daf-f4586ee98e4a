package br.com.alice.common.core.exceptions

import io.ktor.http.HttpStatusCode

/**
 * This exception cannot be used in the RFC, only inside domain-services or bff-api
 */
open class AliceException(
    message: String,
    val code: String,
    cause: Throwable? = null
) : Exception(message, cause)

abstract class RfcException(
    message: String,
    code: String = "unknown_error",
    cause: Throwable? = null
): AliceException(message, code, cause) {
    abstract val statusCode : HttpStatusCode

    constructor() : this("RFC Error")
}
