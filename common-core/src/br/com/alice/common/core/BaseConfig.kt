package br.com.alice.common.core

import br.com.alice.common.core.extensions.toKebabCase
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

open class BaseConfig(val config: HoconApplicationConfig) {

    companion object {
        val instance = BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.commons.conf")))
    }

    private val commonConfig = HoconApplicationConfig(ConfigFactory.load("application.commons.conf"))

    val runningMode: RunningMode = RunningMode.valueOf(commonConfig.property("systemEnv").getString().uppercase())

    protected val environment = commonConfig.propertyOrNull("environment")?.getString() ?: "prod"

    val awsAccount = commonConfig.property("aws.account").getString()
    private val awsRegion = commonConfig.property("aws.region").getString()

    protected fun config(key: String) = config
        .property("${runningMode.toString().lowercase()}.$key")
        .getString()

    protected fun globalConfig(key: String) = config
        .property("$key")
        .getString()

    val firebaseTokenJson = commonConfig.property("auth.firebaseTokenJson").getString()
    val firebaseClientX509CertUrl = commonConfig.property("auth.firebaseClientX509CertUrl").getString()

    fun topic(name: String) =
        config.propertyOrNull("${runningMode.value}.topics.$name")?.getString()
            ?: "arn:aws:sns:$awsRegion:$awsAccount:$environment-${name.toKebabCase()}-event".lowercase()

    fun fileVaultUrl() = commonConfig.property("${runningMode.value}.fileVaultUrl").getString()

    fun url(path: String): String {
        val baseUrl = config("baseUrl")
        return "$baseUrl$path"
    }

    val isProduction = environment == "prod"

    val isStaging = environment == "staging"

    val isK8s = commonConfig.propertyOrNull("k8sEnv")?.getString().toBoolean()

    val jaegerEndpoint = commonConfig.property("jaegerEndpoint").getString()

    val dataEventApiUrl = commonConfig.property("${runningMode.value}.dataEventApiUrl").getString()

}
