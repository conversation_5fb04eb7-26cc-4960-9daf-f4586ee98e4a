package br.com.alice.amas.api.ioc

import br.com.alice.amas.api.SERVICE_NAME
import br.com.alice.amas.api.controller.v2.HealthSpecialistResourceBundleController
import br.com.alice.amas.api.controller.v2.InvoiceController
import br.com.alice.amas.api.controller.v2.InvoiceCritiqueController
import br.com.alice.amas.api.controller.v2.NationalReceiptController
import br.com.alice.amas.api.controller.v2.StaffController
import br.com.alice.amas.api.services.InvoiceHistoryService
import br.com.alice.amas.api.services.InvoiceResponseService
import br.com.alice.common.controllers.HealthController
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module

val ServiceModule = module(createdAtStart = true) {
    // Configuration

    // Configuration
    single { HoconApplicationConfig(ConfigFactory.load("application.conf")) }

    // Controllers
    single { HealthController(SERVICE_NAME) }

    // Controllers V2
   singleOf(::InvoiceController)
   singleOf(::InvoiceCritiqueController)
   singleOf(::NationalReceiptController)
   singleOf(::HealthSpecialistResourceBundleController)
    singleOf(::StaffController)

    // Services
    singleOf(::InvoiceResponseService)
    singleOf(::InvoiceHistoryService)
}
