package br.com.alice.staff.services

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.service.data.dsl.OrPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.or
import br.com.alice.common.service.data.dsl.scope
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.CouncilModel
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistAppointmentType.PRESENTIAL
import br.com.alice.data.layer.models.SpecialistAppointmentType.REMOTE
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.data.layer.models.StaffModel
import br.com.alice.data.layer.models.withStaff
import br.com.alice.data.layer.services.HealthProfessionalModelDataService
import br.com.alice.staff.client.ContactService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.HealthProfessionalChangedEvent
import br.com.alice.staff.event.HealthProfessionalUpdatedEvent
import br.com.alice.staff.logics.HealthProfessionalPredicatesLogic
import br.com.alice.staff.models.StaffWithHealthProfessional
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class HealthProfessionalServiceImpl(
    private val healthProfessionalDataService: HealthProfessionalModelDataService,
    private val staffService: StaffService,
    private val contactService: ContactService,
    private val kafkaProducerService: KafkaProducerService
) : HealthProfessionalService {

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun get(id: UUID, findOptions: FindOptions): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.findOne {
            where {
                this.id.eq(id).withFilter(findOptions.status) {
                    this.status.inList(findOptions.status!!)
                }!!
            }
        }.map { withStaff(it, findOptions) }
            .map { withContact(it, findOptions) }
            .map { it.toTransport() }

    override suspend fun getByIds(
        ids: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService
            .find { where { this.id.inList(ids) } }
            .flatMap { healthProfessionals ->
                staffService.findByList(healthProfessionals.map { it.staffId })
                    .map { staffs -> staffs.associateBy { it.id } }
                    .map { staffsMap ->
                        healthProfessionals.map { healthProfessional ->
                            healthProfessional.withStaff(
                                staffsMap[healthProfessional.staffId]?.toModel()
                            )
                        }
                    }
            }.mapEach { it.toTransport() }

    override suspend fun getByRoleAndRange(
        role: Role,
        range: IntRange
    ): Result<List<HealthProfessional>, Throwable> =
        staffService.findByRoleAndRange(role, range)
            .map { staffs -> staffs.associateBy { it.id } }
            .flatMap { staffsMap ->
                healthProfessionalDataService.find {
                    where { this.staffId.inList(staffsMap.keys.toList()) }
                }.pmapEach { healthProfessional ->
                    healthProfessional.withStaff(
                        staffsMap[healthProfessional.staffId]?.toModel()
                    )
                }
            }.mapEach { it.toTransport() }

    override suspend fun getByStaffIds(
        staffIds: List<UUID>
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService
            .find { where { this.staffId.inList(staffIds) } }
            .flatMap { healthProfessionals ->

                staffService.findByList(healthProfessionals.map { it.staffId })
                    .map { staffs -> staffs.associateBy { it.id } }
                    .map { staffsMap ->
                        healthProfessionals.map { healthProfessional ->
                            healthProfessional.withStaff(
                                staffsMap[healthProfessional.staffId]?.toModel()
                            )
                        }
                    }
            }.mapEach { it.toTransport() }

    override suspend fun findByStaffIds(
        staffIds: List<UUID>
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService
            .find { where { this.staffId.inList(staffIds) } }
            .mapEach { it.toTransport() }

    override suspend fun findByStaffId(staffId: UUID, findOptions: FindOptions): Result<HealthProfessional, Throwable> =
        getByStaffId(staffId, findOptions)
            .map { withStaff(it, findOptions) }
            .map { withContact(it, findOptions) }
            .map { it.toTransport() }

    override suspend fun getByUrlSlugAndRoles(
        urlSlug: String,
        roles: List<Role>
    ): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.findOne { where { this.urlSlug.eq(urlSlug) } }
            .map { it.withStaff(staffService.getActiveWithRole(it.staffId, roles).getOrNull()?.toModel()) }
            .map { it.toTransport() }

    override suspend fun getByUrlSlug(
        urlSlug: String
    ): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.findOne { where { this.urlSlug.eq(urlSlug) } }.map { it.toTransport() }

    override suspend fun searchByNameAndRoleWithRange(
        staffIds: List<UUID>?,
        range: IntRange,
        roles: List<Role>,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>?
    ): Result<List<HealthProfessional>, Throwable> =
        staffService.searchByNameAndRoleWithRange(staffIds, range, roles, namePrefix, active, types)
            .flatMapPair { staffs ->
                healthProfessionalDataService.find { where { this.staffId.inList(staffs.map { it.id }) } }
            }
            .map { (healthProfessionals, staffs) ->
                val staffsMap = staffs.associateBy { it.id }
                healthProfessionals.map { healthProfessional ->
                    healthProfessional.withStaff(
                        staffsMap[healthProfessional.staffId]?.toModel()
                    )
                }
            }.mapEach { it.toTransport() }

    override suspend fun findBySubSpecialtyId(
        subSpecialtyId: UUID,
    ): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.findOne {
            where {
                this.subSpecialtyIds.contains(subSpecialtyId)
            }
        }.map { it.toTransport() }

    override suspend fun searchByAppointmentTypeSpecialtyTiersAndStaffIds(
        showOnApp: Boolean?,
        appointmentTypes: List<SpecialistAppointmentType>?,
        specialtyTiers: List<SpecialtyTiers>,
        staffIds: List<UUID>,
        range: IntRange
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where {
                this.showOnApp.eq(showOnApp ?: true) and
                        this.appointmentTypes.containsAny(appointmentTypes ?: listOf(REMOTE, PRESENTIAL)) and
                        this.staffId.inList(staffIds) and
                        renderSpecialtyFilters(specialtyTiers)
            }.offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun countByAppointmentTypeSpecialtyTiersAndStaffIds(
        showOnApp: Boolean?,
        appointmentTypes: List<SpecialistAppointmentType>?,
        specialtyTiers: List<SpecialtyTiers>,
        staffIds: List<UUID>,
    ): Result<Int, Throwable> =
        healthProfessionalDataService.count {
            where {
                this.showOnApp.eq(showOnApp ?: true) and
                        this.appointmentTypes.containsAny(appointmentTypes ?: listOf(REMOTE, PRESENTIAL)) and
                        this.staffId.inList(staffIds) and
                        renderSpecialtyFilters(specialtyTiers)
            }
        }

    override suspend fun countByNameAndRoleWithRange(
        staffIds: List<UUID>?,
        roles: List<Role>,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>?
    ): Result<Int, Throwable> =
        staffService.countByNameAndRoleWithRange(staffIds, roles, namePrefix, active, types)

    override suspend fun setOnCall(staffId: UUID, onCall: Boolean): Result<HealthProfessional, Throwable> =
        getByStaffId(staffId)
            .flatMap {
                healthProfessionalDataService.update(it.copy(onCall = onCall))
            }.map { it.toTransport() }

    override suspend fun findBySpecialtyIds(
        specialtyIds: List<UUID>
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where { this.specialtyId.inList(specialtyIds) }
        }.flatMap { healthProfessionals ->
            staffService.findByList(healthProfessionals.map { it.staffId })
                .map { staffs -> staffs.associateBy { it.id } }
                .map { staffsMap ->
                    healthProfessionals.map { healthProfessional ->
                        healthProfessional.withStaff(
                            staffsMap[healthProfessional.staffId]?.toModel()
                        )
                    }
                }
        }.mapEach { it.toTransport() }

    override suspend fun getByIdsAndSpecialtyIds(
        ids: List<UUID>,
        specialtyIds: List<UUID>,
    ): Result<List<StaffWithHealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where {
                this.staffId.inList(ids)
                    .and(this.specialtyId.inList(specialtyIds))
            }
        }.flatMap { healthProfessionals ->
            staffService.findByList(healthProfessionals.map { it.staffId })
                .map { staffs -> staffs.associateBy { it.id } }
                .map { staffsMap ->
                    healthProfessionals.map { healthProfessional ->
                        StaffWithHealthProfessional(
                            staffsMap[healthProfessional.staffId]!!,
                            healthProfessional.toTransport()
                        )
                    }
                }
        }

    override suspend fun findBySpecialtyIdsAndRole(
        specialtyIds: List<UUID>,
        role: Role
    ): Result<List<HealthProfessional>, Throwable> =
        staffService.findWithAnyRole(listOf(role))
            .map { staffs -> staffs.associateBy { it.id } }
            .flatMap { staffsMap ->
                healthProfessionalDataService.find {
                    where { this.specialtyId.inList(specialtyIds) and this.staffId.inList(staffsMap.keys.toList()) }
                }.pmapEach {
                    it.withStaff(staffsMap[it.staffId]?.toModel())
                }
            }.mapEach { it.toTransport() }

    override suspend fun findByInternalSpecialties(
        internalSpecialtyIds: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable> =
        getByInternalSpecialty(internalSpecialtyIds).map { (staffs, hps) ->
            staffs.associateBy { it.id } to hps
        }.map { (staffsMap, hps) ->
            hps.mapNotNull { hp ->
                staffsMap[hp.staffId]?.let {
                    hp.withStaff(it)
                }
            }
        }.mapEach { it.toTransport() }

    override suspend fun findActivesByInternalAndExternalSpecialties(specialtyIds: List<UUID>): Result<List<HealthProfessional>, Throwable> =
        getBySpecialties(specialtyIds).map { (staffs, hps) ->
            staffs.associateBy { it.id } to hps
        }.map { (staffsMap, hps) ->
            hps.mapNotNull { hp ->
                staffsMap[hp.staffId]?.let {
                    hp.withStaff(it)
                }
            }
        }.mapEach { it.toTransport() }

    override suspend fun findActivesWithAnyRole(roles: List<Role>): Result<List<HealthProfessional>, Throwable> =
        staffService.findActivesWithAnyRole(roles).map { staffs ->
            staffs.associateBy { it.id }
        }.flatMap { staffsMap ->
            healthProfessionalDataService.find { where { this.staffId.inList(staffsMap.keys.toList()) } }
                .pmapEach { healthProfessional ->
                    healthProfessional.withStaff(staffsMap[healthProfessional.staffId]?.toModel())
                }
        }.mapEach { it.toTransport() }

    override suspend fun findStaffsWithHealthProfessionalIfExists(
        staffIds: List<UUID>
    ): Result<List<StaffWithHealthProfessional>, Throwable> =
        coroutineScope {
            val staffDeferred = async { staffService.findByList(staffIds).get() }
            val healthProfessionalDeferred = async {
                healthProfessionalDataService.find { where { this.staffId.inList(staffIds) } }
                    .get()
                    .associateBy { it.staffId }
            }

            val healthProfessionalMap = healthProfessionalDeferred.await()

            staffDeferred.await().map {
                StaffWithHealthProfessional(it, healthProfessionalMap[it.id]?.toTransport())
            }.success()
        }

    override suspend fun countBySpecialtyIdAndTier(
        specialty: UUID,
        tier: SpecialistTier
    ): Result<Int, Throwable> =
        healthProfessionalDataService.count {
            where { specialtyId.eq(specialty).and(this.tier.eq(tier)) }
        }

    override suspend fun countBySpecialtyIdsAndTiers(
        specialtyIdList: List<UUID>,
        tiersList: List<SpecialistTier>
    ): Result<Int, Throwable> =
        healthProfessionalDataService.count {
            where {
                specialtyId.inList(specialtyIdList).and(this.tier.inList(tiersList))
            }
        }

    override suspend fun getBySpecialtyIdsAndTiers(
        specialtyIdList: List<UUID>,
        tiersList: List<SpecialistTier>
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where {
                specialtyId.inList(specialtyIdList)
                    .and(this.tier.inList(tiersList))
                    .and(showOnApp.eq(true))
            }
        }.mapEach { it.toTransport() }

    override suspend fun existsByCouncil(council: Council): Result<Boolean, Throwable> =
        healthProfessionalDataService.exists {
            where { this.council.eq(CouncilModel(council.number, council.state)) }
        }

    override suspend fun inactivateById(id: UUID): Result<Boolean, Throwable> = span("inactivateById") {
        healthProfessionalDataService.get(id)
            .flatMap { healthProfessionalDataService.update(it.copy(status = SpecialistStatus.INACTIVE)) }
            .map { it.toTransport() }
            .then {
                kafkaProducerService.produce(HealthProfessionalUpdatedEvent(it.id))
                kafkaProducerService.produce(HealthProfessionalChangedEvent(it, NotificationEventAction.UPDATED))
            }
            .map { true }
            .coFoldNotFound { false.success() }
    }

    override suspend fun changeShowOnApp(id: UUID, showOnApp: Boolean): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.get(id)
            .flatMap { healthProfessionalDataService.update(it.copy(showOnApp = showOnApp)) }
            .map { it.toTransport() }
            .then {
                kafkaProducerService.produce(HealthProfessionalUpdatedEvent(id))
                kafkaProducerService.produce(HealthProfessionalChangedEvent(it, NotificationEventAction.UPDATED))
            }

    override suspend fun getByRange(range: IntRange): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            offset { range.first }
                .limit { range.count() }
                .orderBy { createdAt }
        }.mapEach { it.toTransport() }

    override suspend fun getSpecialistsByRange(range: IntRange): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where { this.type.eq(StaffType.COMMUNITY_SPECIALIST) }
                .offset { range.first }
                .limit { range.count() }
                .orderBy { createdAt }
        }.mapEach { it.toTransport() }

    override suspend fun updateVacation(
        id: UUID,
        startDate: LocalDateTime?,
        endDate: LocalDateTime?
    ): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.get(id)
            .flatMap {
                healthProfessionalDataService.update(
                    it.copy(onVacationStart = startDate, onVacationUntil = endDate)
                )
            }.map { it.toTransport() }.then {
                kafkaProducerService.produce(HealthProfessionalUpdatedEvent(it.id))
                kafkaProducerService.produce(HealthProfessionalChangedEvent(it, NotificationEventAction.UPDATED))
            }

    @OptIn(WithFilterPredicateUsage::class)
    private suspend fun getByStaffId(staffId: UUID, findOptions: FindOptions? = null) =
        healthProfessionalDataService.findOne {
            where {
                this.staffId.eq(staffId).withFilter(findOptions?.status) {
                    this.status.inList(findOptions?.status!!)
                }!!
            }
        }

    private suspend fun getByInternalSpecialty(
        internalSpecialtyIds: List<UUID>
    ): Result<Pair<List<StaffModel>, List<HealthProfessionalModel>>, Throwable> =
        healthProfessionalDataService.find {
            where { this.internalSpecialtyId.inList(internalSpecialtyIds) }
        }.flatMapPair { hps ->
            staffService.findActivesById(hps.map { it.staffId }).mapEach { it.toModel() }
        }

    @OptIn(OrPredicateUsage::class)
    private suspend fun getBySpecialties(
        specialtyIds: List<UUID>
    ): Result<Pair<List<StaffModel>, List<HealthProfessionalModel>>, Throwable> =
        healthProfessionalDataService.find {
            where {
                this.status.eq(SpecialistStatus.ACTIVE).and(
                    scope(
                        this.internalSpecialtyId.inList(specialtyIds) or this.specialtyId.inList(specialtyIds)
                    )
                )
            }
        }.flatMapPair { hps ->
            staffService.findActivesById(hps.map { it.staffId }).mapEach { it.toModel() }
        }

    override suspend fun findByEmail(email: String, active: SpecialistStatus): Result<HealthProfessional, Throwable> =
        healthProfessionalDataService.findOne {
            where {
                this.email.eq(email).and(this.status.eq(active))
            }
        }.map { it.toTransport() }

    @OptIn(OrPredicateUsage::class)
    private fun renderSpecialtyFilters(specialtyTiersList: List<SpecialtyTiers>): Predicate =
        scope(
            specialtyTiersList.fold(scope(Predicate.False)) { oldPredicate, specialtyTiers ->
                oldPredicate.or(
                    scope(
                        HealthProfessionalModelDataService.FieldOptions().tier.inList(specialtyTiers.tiers).and(
                            HealthProfessionalModelDataService.FieldOptions().specialtyId.eq(specialtyTiers.specialtyId)
                        )
                    )
                )
            }
        )

    private suspend fun withStaff(
        healthProfessional: HealthProfessionalModel,
        findOption: FindOptions
    ): HealthProfessionalModel =
        if (findOption.withStaff) healthProfessional.withStaff(
            staffService.get(healthProfessional.staffId).getOrNullIfNotFound()?.toModel()
        )
        else healthProfessional


    private suspend fun withContact(
        healthProfessional: HealthProfessionalModel,
        findOption: FindOptions
    ): HealthProfessionalModel =
        if (findOption.withContact) {
            val contacts =
                contactService.findByIds(
                    healthProfessional.contactIds,
                    ContactService.FieldOptions(includeAddress = true)
                ).get()
            healthProfessional.copy(
                contacts = contacts.map { it.toModel() },
                addressesStructured = contacts.mapNotNull { it.address }
            )
        } else healthProfessional

    override suspend fun searchByNameAndIdWithRange(
        range: IntRange,
        namePrefix: String?,
        ids: List<UUID>?
    ): Result<List<HealthProfessional>, Throwable> = useReadDatabase {
        healthProfessionalDataService.find {
            HealthProfessionalPredicatesLogic.buildFilterQuery(
                range = range,
                namePrefix = namePrefix,
                ids = ids
            )
        }.mapEach { it.toTransport() }
    }

    override suspend fun countByNameAndId(
        namePrefix: String?,
        ids: List<UUID>?
    ): Result<Int, Throwable> =
        healthProfessionalDataService.count {
            HealthProfessionalPredicatesLogic.buildFilterQuery(
                namePrefix = namePrefix,
                ids = ids
            )
        }

    override suspend fun getByFilterAndRange(
        namePrefix: String,
        statusFilter: SpecialistStatus,
        range: IntRange,
    ): Result<List<HealthProfessional>, Throwable> =
        healthProfessionalDataService.find {
            where {
                this.searchTokens.search(namePrefix) and
                        this.status.eq(statusFilter)
            }.offset { range.first }.limit { range.count() }
        }.mapEach { it.toTransport() }

    override suspend fun countByFilter(
        namePrefix: String,
        statusFilter: SpecialistStatus
    ): Result<Int, Throwable> =
        healthProfessionalDataService.count {
            where {
                this.searchTokens.search(namePrefix) and
                        this.status.eq(statusFilter)
            }
        }

    override suspend fun countAllSpecialists(): Result<Int, Throwable> = useReadDatabase {
        healthProfessionalDataService.count { where { this.type.eq(StaffType.COMMUNITY_SPECIALIST) } }
    }

}
