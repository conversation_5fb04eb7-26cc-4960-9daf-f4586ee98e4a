package br.com.alice.staff.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.SpecialistAppointmentType

object HealthProfessionalConverter : Converter<HealthProfessionalModel, HealthProfessional>(
    HealthProfessionalModel::class, HealthProfessional::class
) {
    fun unconvert(source: HealthProfessional): HealthProfessionalModel =
        HealthProfessionalConverter.unconvert(
            source,
            map(HealthProfessionalModel::contacts) from source.contacts?.map { it.toModel() },
            map(HealthProfessionalModel::bankAccountInfo) from source.bankAccountInfo?.map { it.toModel() },
        )

    fun convert(source: HealthProfessionalModel): HealthProfessional =
        HealthProfessionalConverter.convert(
            source,
            map(HealthProfessional::contacts) from source.contacts?.map { it.toTransport() },
            map(HealthProfessional::bankAccountInfo) from source.bankAccountInfo?.map { it.toTransport() },
        )
}

fun HealthProfessionalModel.toTransport() = HealthProfessionalConverter.convert(this)
fun HealthProfessional.toModel() = HealthProfessionalConverter.unconvert(this)
