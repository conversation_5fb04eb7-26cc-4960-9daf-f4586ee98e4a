package br.com.alice.member.api.builders

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.member.api.builders.PortabilityMiscBuilder
import br.com.alice.member.api.builders.PortabilityQuestionResponseBuilder
import br.com.alice.member.api.builders.ProductOrderBuilder
import br.com.alice.member.api.models.BasicNavigationResponse
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.PortabilityQuestionResponse
import br.com.alice.member.api.models.onboarding.ProductOrderResponse
import br.com.alice.member.api.models.onboarding.QuestionInputActionResponse
import br.com.alice.member.api.models.onboarding.QuestionInputConfigResponse
import br.com.alice.member.api.models.onboarding.QuestionInputOptionResponse
import br.com.alice.member.api.models.onboarding.QuestionInputResponse
import br.com.alice.member.api.models.onboarding.QuestionInputType
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.product.model.ProductWithProviders
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PortabilityQuestionResponseBuilderTest {

    private val productWithProviders = ProductWithProviders(
        product = TestModelFactory.buildProduct(),
        providers = TestModelFactory.buildProviders()
    )

    private val person = TestModelFactory.buildPerson()
    private val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product = productWithProviders.product)

    @Test
    fun `#buildPortabilityOptinQuestionResponse should return response as expected`() = runBlocking {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)
        val productOrderResponse: ProductOrderResponse = mockk()

        mockkObject(ProductOrderBuilder) {
            coEvery { ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct) } returns productOrderResponse

            val expectedResponse = PortabilityQuestionResponse(
                question = "Na Alice você pode fazer portabilidade das suas carências do seu plano atual.",
                input = QuestionInputResponse(
                    type = QuestionInputType.OPTION_BUTTONS,
                    action = QuestionInputActionResponse(
                        href = "http://localhost/onboarding/portability/questions/eligibility"
                    ),
                    options = listOf(
                        QuestionInputOptionResponse(
                            label = "Quero saber se sou elegível",
                            value = "confirm"
                        ),
                        QuestionInputOptionResponse(
                            label = "Não quero",
                            value = "skip"
                        ),
                    )
                ),
                productOrder = productOrderResponse,
                detail = PortabilityMiscBuilder.getPortabilityRules()
            )

            val actualResponse = PortabilityQuestionResponseBuilder.buildPortabilityOptinQuestionResponse(person, orderWithProduct)
            assertThat(actualResponse).isEqualTo(expectedResponse)
        }
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is CREATED and question is MIN_GRACE_PERIOD`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.CREATED,
            answers = emptyList(),
        )

        val productOrderResponse: ProductOrderResponse = mockk()

        mockkObject(ProductOrderBuilder) {
            coEvery { ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct) } returns productOrderResponse

            val expectedResponse = PortabilityQuestionResponse(
                question = "Você tem plano de saúde há mais de 2 anos?",
                detail = "Não precisa estar no mesmo plano há 2 anos.",
                input = QuestionInputResponse(
                    type = QuestionInputType.OPTION_BUTTONS,
                    action = QuestionInputActionResponse(
                        href = "http://localhost/onboarding/portability/questions/min_grace_period"
                    ),
                    options = listOf(
                        QuestionInputOptionResponse(
                            label = "Sim",
                            value = "true"
                        ),
                        QuestionInputOptionResponse(
                            label = "Não",
                            value = "false"
                        ),
                    )
                ),
                productOrder = productOrderResponse
            )

            val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
            assertThat(actualResponse).isEqualTo(expectedResponse)
        }
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is CREATED and question is CURRENT_HEALTH_INSURANCE`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.CREATED,
            answers = listOf(
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD)
            ),
        )

        val productOrderResponse: ProductOrderResponse = mockk()

        mockkObject(ProductOrderBuilder) {
            coEvery { ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct) } returns productOrderResponse

            val expectedResponse = PortabilityQuestionResponse(
                question = "Qual é o seu plano de saúde atual?",
                input = QuestionInputResponse(
                    type = QuestionInputType.FREE_TEXT,
                    action = QuestionInputActionResponse(
                        href = "http://localhost/onboarding/portability/questions/current_health_insurance"
                    ),
                    config = QuestionInputConfigResponse(
                        hint = "Ex: Sulamérica"
                    )
                ),
                productOrder = productOrderResponse
            )

            val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
            assertThat(actualResponse).isEqualTo(expectedResponse)
        }
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is CREATED and question is CURRENT_HEALTH_INSURANCE_HOSPITALS`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.CREATED,
            answers = listOf(
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE)
            ),
        )

        val productOrderResponse: ProductOrderResponse = mockk()

        mockkObject(ProductOrderBuilder) {
            coEvery { ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct) } returns productOrderResponse

            val expectedResponse = PortabilityQuestionResponse(
                question = "No seu plano atual, você tem acesso a algum desses hospitais?",
                input = QuestionInputResponse(
                    type = QuestionInputType.MULTIPLE_OPTIONS,
                    action = QuestionInputActionResponse(
                        href = "http://localhost/onboarding/portability/questions/current_health_insurance_hospitals"
                    ),
                    options = listOf(
                        QuestionInputOptionResponse(
                            label = "Hospital Alemão Oswaldo Cruz",
                            value = "haoc",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "Hospitais Rede D'Or São Luiz",
                            value = "sao_luiz",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "A Beneficência Portuguesa de São Paulo",
                            value = "beneficiencia_portuguesa",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "Hospital Israelita Albert Einstein",
                            value = "albert_einstein",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "Hospital Sírio Libanês",
                            value = "sirio_libanes",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "Hospital Santa Catarina",
                            value = "santa_catarina",
                            exclusive = false,
                        ),
                        QuestionInputOptionResponse(
                            label = "Nenhum deles",
                            value = "NONE",
                            exclusive = true,
                        ),
                    )
                ),
                productOrder = productOrderResponse
            )

            val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
            assertThat(actualResponse).isEqualTo(expectedResponse)
        }
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is CREATED and question is null`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.CREATED,
            answers = listOf(
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE_HOSPITALS),
            ),
        )

        val expectedResponse = BasicNavigationResponse(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_STATUS,
                link = Links.PORTABILITY_STATUS
            )
        )

        val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
        assertThat(actualResponse).isEqualTo(expectedResponse)
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is PENDING`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.PENDING,
        )

        val expectedResponse = BasicNavigationResponse(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_STATUS,
                link = Links.PORTABILITY_STATUS
            )
        )

        val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
        assertThat(actualResponse).isEqualTo(expectedResponse)
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is APPROVED`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.APPROVED,
        )

        val expectedResponse = BasicNavigationResponse(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_STATUS,
                link = Links.PORTABILITY_STATUS
            )
        )

        val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
        assertThat(actualResponse).isEqualTo(expectedResponse)
    }

    @Test
    fun `#buildQuestionResponse should return response as expected when status is DECLINED`() {
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            status = InsurancePortabilityRequestStatus.DECLINED,
        )

        val expectedResponse = BasicNavigationResponse(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_STATUS,
                link = Links.PORTABILITY_STATUS
            )
        )

        val actualResponse = PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest)
        assertThat(actualResponse).isEqualTo(expectedResponse)
    }
}
