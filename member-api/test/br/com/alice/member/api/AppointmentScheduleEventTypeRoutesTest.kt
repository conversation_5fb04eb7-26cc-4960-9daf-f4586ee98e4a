package br.com.alice.member.api

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.member.api.controllers.AppointmentScheduleEventTypeController
import br.com.alice.member.api.models.AppointmentScheduleEventTypeResponse
import br.com.alice.member.api.models.AppointmentScheduleEventTypeSetupResponse
import br.com.alice.member.api.models.AppointmentScheduleEventTypesSetupResponse
import br.com.alice.member.api.models.EventProviderUnitResponse
import br.com.alice.member.api.models.ProviderUnitSetupResponse
import br.com.alice.member.api.models.StaffResponse
import br.com.alice.member.api.models.StaffsRelatedToActiveConditionsResponse
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.HealthConditionSchedulingService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentScheduleEventTypeRoutesTest : RoutesTestHelper() {

    private val service: AppointmentScheduleEventTypeService = mockk()
    private val eventTypeProviderUnitService: EventTypeProviderUnitService = mockk()
    private val staffService: StaffService = mockk()
    private val healthConditionSchedulingService: HealthConditionSchedulingService = mockk()
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val internalAppointmentScheduleService: br.com.alice.member.api.services.AppointmentScheduleService =
        mockk()

    private val person = toTestPerson(TestModelFactory.buildPerson())

    private val staff = TestModelFactory.buildStaff()
    private val token = RangeUUID.generate().toString()

    private val staffResponse = StaffResponse(
        id = staff.id,
        firstName = staff.firstName,
        lastName = staff.lastName,
        role = staff.role.toString(),
        profileImageUrl = staff.profileImageUrl,
        description = staff.role.description
    )

    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        duration = 30,
        title = "Coleta",
        isMultiProfessionalReferral = true,
        numberOfDaysFromNowToAllowScheduling = 35,
    )

    private val appointmentScheduleEventTypeNominal = TestModelFactory.buildAppointmentScheduleEventType(
        duration = 30,
        title = "Coleta",
        isMultiProfessionalReferral = false,
        numberOfDaysFromNowToAllowScheduling = 35,
    )

    private val otherAppointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        duration = 30,
        title = "Consulta",
        isMultiProfessionalReferral = false,
        numberOfDaysFromNowToAllowScheduling = 35,
        locationType = AppointmentScheduleEventTypeLocation.ON_SITE
    )

    private val otherAppointmentScheduleEventTypeGeneric = TestModelFactory.buildAppointmentScheduleEventType(
        duration = 30,
        title = "Consulta",
        isMultiProfessionalReferral = true,
        numberOfDaysFromNowToAllowScheduling = 35,
        locationType = AppointmentScheduleEventTypeLocation.ON_SITE
    )

    private val providerUnitId = "29f2b57c-2f9b-4a13-8123-f285b4a0f119".toUUID()
    private val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)
    private val eventTypeProviderUnit = TestModelFactory.buildEventTypeProviderUnit(
        providerUnitId = providerUnitId,
        appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
        duration = 20,
        numberOfDaysFromNowToAllowScheduling = 20
    )

    private val personCase = TestModelFactory.buildPersonCase(
        responsibleStaffId = staff.id,
        personId = person.id,
    )

    companion object {
        private val staff = TestModelFactory.buildStaff()
        private val remoteAppointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
            duration = 30,
            title = "Coleta",
            isMultiProfessionalReferral = true,
            numberOfDaysFromNowToAllowScheduling = 35,
        )
        private val onSiteAppointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
            duration = 30,
            title = "Consulta",
            isMultiProfessionalReferral = false,
            numberOfDaysFromNowToAllowScheduling = 35,
            locationType = AppointmentScheduleEventTypeLocation.ON_SITE
        )
        private val eventTypeIds = listOf(remoteAppointmentScheduleEventType.id, onSiteAppointmentScheduleEventType.id)
        private val providerUnitId = "29f2b57c-2f9b-4a13-8123-f285b4a0f110".toUUID()
        private val eventTypeProviderUnit = TestModelFactory.buildEventTypeProviderUnit(
            providerUnitId = providerUnitId,
            appointmentScheduleEventTypeId = remoteAppointmentScheduleEventType.id,
            duration = 20,
            numberOfDaysFromNowToAllowScheduling = 20
        )

        @JvmStatic
        private fun getAppointmentScheduleEventTypeSetupParams() = listOf(
            // with: remote and onsite event types
            // without: staffId
            arrayOf(
                null,
                remoteAppointmentScheduleEventType,
                onSiteAppointmentScheduleEventType,
                eventTypeIds,
                eventTypeProviderUnit,
                AppointmentScheduleEventTypesSetupResponse(
                    events = listOf(
                        AppointmentScheduleEventTypeSetupResponse(
                            id = remoteAppointmentScheduleEventType.id,
                            duration = 20,
                            numberOfDaysFromNowToAllowScheduling = 20,
                            providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                            isSelected = true,
                            isMultiProfessionalReferral = true,
                            title = "Coleta",
                            category = remoteAppointmentScheduleEventType.category,
                        )
                    ),
                    staff = null
                )
            ),
            // with: remote and onsite event types, appointmentSchedule and staff
            // without: staffId
            arrayOf(
                staff.id,
                remoteAppointmentScheduleEventType,
                onSiteAppointmentScheduleEventType,
                eventTypeIds,
                eventTypeProviderUnit,
                AppointmentScheduleEventTypesSetupResponse(
                    events = listOf(
                        AppointmentScheduleEventTypeSetupResponse(
                            id = remoteAppointmentScheduleEventType.id,
                            duration = 20,
                            numberOfDaysFromNowToAllowScheduling = 20,
                            providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                            isSelected = true,
                            isMultiProfessionalReferral = true,
                            title = "Coleta",
                            category = remoteAppointmentScheduleEventType.category,
                        ),
                        AppointmentScheduleEventTypeSetupResponse(
                            id = onSiteAppointmentScheduleEventType.id,
                            duration = 20,
                            numberOfDaysFromNowToAllowScheduling = 20,
                            providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                            isSelected = false,
                            isMultiProfessionalReferral = false,
                            title = "Consulta",
                            category = onSiteAppointmentScheduleEventType.category,
                        )
                    ),
                    staff = StaffResponse(
                        staff.id,
                        staff.firstName,
                        staff.lastName,
                        staff.profileImageUrl,
                        staff.role.name,
                        staff.role.description
                    )
                )
            )
        )
    }

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            AppointmentScheduleEventTypeController(
                service,
                eventTypeProviderUnitService,
                healthConditionSchedulingService,
                staffService,
                providerUnitService,
                appointmentScheduleOptionService,
                internalAppointmentScheduleService
            )
        }

        coEvery { staffService.get(staff.id) } returns staff
    }

    @Test
    fun `#getAppointmentScheduleEventType should return appointment schedule event type with default availability when providerUnitId is not provided as parameter`() {
        val expectedResponse = AppointmentScheduleEventTypeResponse(
            duration = 30,
            title = "Coleta",
            isMultiProfessionalReferral = true,
            numberOfDaysFromNowToAllowScheduling = 35
        )

        coEvery {
            service.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/${appointmentScheduleEventType.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#getAppointmentScheduleEventType should return appointment schedule event type with availability based on provider unit`() {
        val expectedResponse = AppointmentScheduleEventTypeResponse(
            duration = 20,
            title = "Coleta",
            isMultiProfessionalReferral = true,
            numberOfDaysFromNowToAllowScheduling = 20
        )

        coEvery {
            service.get(appointmentScheduleEventType.id)
        } returns appointmentScheduleEventType

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/${appointmentScheduleEventType.id}/?providerUnitId=$providerUnitId"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#getStaffsRelatedToActiveCondition should return staffs related to active conditions from member related to event type`() {
        coEvery {
            healthConditionSchedulingService.getConditionsRelatedToEventTypeForScheduling(
                person.id,
                appointmentScheduleEventType.id
            )
        } returns listOf(personCase)

        coEvery {
            staffService.findByList(listOf(staff.id))
        } returns listOf(staff)

        val expectedResponse = StaffsRelatedToActiveConditionsResponse(
            staffs = listOf(
                StaffResponse(
                    staff.id,
                    staff.firstName,
                    staff.lastName,
                    staff.profileImageUrl,
                    staff.role.name,
                    staff.role.description
                )
            )
        )

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/${appointmentScheduleEventType.id}/staffs_related_to_conditions"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#getStaffsRelatedToActiveCondition should return empty staff list when member has no conditions`() {
        coEvery {
            healthConditionSchedulingService.getConditionsRelatedToEventTypeForScheduling(
                person.id,
                appointmentScheduleEventType.id
            )
        } returns emptyList()

        val expectedResponse = StaffsRelatedToActiveConditionsResponse(
            staffs = emptyList()
        )

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/${appointmentScheduleEventType.id}/staffs_related_to_conditions"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup (remote, nominal, without staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = emptyList()
        )

        coEvery {
            service.getByIds(listOf(appointmentScheduleEventTypeNominal.id))
        } returns listOf(appointmentScheduleEventTypeNominal)

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventTypeNominal.id)
        } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventTypeNominal.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
        }
        coVerifyNone {
            staffService.get(any())
            eventTypeProviderUnitService.getForEventType(any())
        }
    }


    @Test
    fun `#webviewSetup should return appointment schedule event type setup (remote, generic, without staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    category = appointmentScheduleEventType.category,
                    title = "Coleta",
                )
            )
        )

        coEvery {
            service.getByIds(listOf(appointmentScheduleEventType.id))
        } returns listOf(appointmentScheduleEventType)

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventType.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
        }
        coVerifyNone {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(any(), any(), any())
            providerUnitService.getByIds(any())
            staffService.get(any())
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(any(), any(), any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup (remote, nominal, with staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    category = appointmentScheduleEventType.category,
                    title = "Coleta",
                )
            ),
            staff = staffResponse
        )

        coEvery {
            service.getByIds(listOf(appointmentScheduleEventType.id))
        } returns listOf(appointmentScheduleEventType)

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))

        coEvery { staffService.get(staff.id) } returns staff

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
            staffService.get(any())
        }
        coVerifyNone {
            providerUnitService.getByIds(any())
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(any(), any(), any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when event is (ON_SITE, nominal, with staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = otherAppointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                    isSelected = true,
                    isMultiProfessionalReferral = false,
                    title = "Consulta",
                    category = otherAppointmentScheduleEventType.category,
                )
            ),
            staff = staffResponse
        )

        coEvery {
            service.getByIds(listOf(otherAppointmentScheduleEventType.id))
        } returns listOf(otherAppointmentScheduleEventType)

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)

        coEvery { staffService.get(staff.id) } returns staff

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${otherAppointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
            staffService.get(any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when event is (ON_SITE, nominal, without staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf()
        )

        coEvery {
            service.getByIds(listOf(otherAppointmentScheduleEventType.id))
        } returns listOf(otherAppointmentScheduleEventType)

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${otherAppointmentScheduleEventType.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
        }
        coVerifyNone {
            staffService.get(any())
            eventTypeProviderUnitService.getForEventType(any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when event is (ON_SITE, generic, with staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = otherAppointmentScheduleEventTypeGeneric.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    title = "Consulta",
                    category = otherAppointmentScheduleEventTypeGeneric.category,
                )
            ),
            staff = staffResponse
        )

        coEvery {
            service.getByIds(listOf(otherAppointmentScheduleEventTypeGeneric.id))
        } returns listOf(otherAppointmentScheduleEventTypeGeneric)

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventTypeGeneric.id)
        } returns listOf(eventTypeProviderUnit)

        coEvery { staffService.get(staff.id) } returns staff

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${otherAppointmentScheduleEventTypeGeneric.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
            staffService.get(any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when event is (ON_SITE, generic, without staff)`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = otherAppointmentScheduleEventTypeGeneric.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    title = "Consulta",
                    category = otherAppointmentScheduleEventTypeGeneric.category,
                )
            )
        )

        coEvery {
            service.getByIds(listOf(otherAppointmentScheduleEventTypeGeneric.id))
        } returns listOf(otherAppointmentScheduleEventTypeGeneric)

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventTypeGeneric.id)
        } returns listOf(eventTypeProviderUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${otherAppointmentScheduleEventTypeGeneric.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
        }
        coVerifyNone {
            staffService.get(any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when category is HealthcareTeam and remote`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    title = "Coleta",
                    category = AppointmentScheduleType.HEALTHCARE_TEAM,
                )
            ),
            staff = staffResponse
        )

        coEvery {
            service.getByIds(listOf(appointmentScheduleEventType.id))
        } returns listOf(appointmentScheduleEventType.copy(category = AppointmentScheduleType.HEALTHCARE_TEAM))

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))

        coEvery { staffService.get(staff.id) } returns staff

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
            staffService.get(any())
        }
    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when category is HealthcareTeam and ON_SITE`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    title = "Coleta",
                    category = AppointmentScheduleType.HEALTHCARE_TEAM,
                )
            ),
            staff = staffResponse
        )


        coEvery {
            service.getByIds(listOf(appointmentScheduleEventType.id))
        } returns listOf(appointmentScheduleEventType.copy(category = AppointmentScheduleType.HEALTHCARE_TEAM))

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)

        coEvery { staffService.get(staff.id) } returns staff

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
            eventTypeProviderUnitService.getForEventType(any())
            staffService.get(any())
        }

    }

    @Test
    fun `#webviewSetup should return appointment schedule event type setup when there are 2 appointmentScheduleEventType`() {
        val expectedResponse = AppointmentScheduleEventTypesSetupResponse(
            events = listOf(
                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.REMOTE,
                    isSelected = true,
                    isMultiProfessionalReferral = true,
                    title = "Coleta",
                    category = appointmentScheduleEventType.category,
                ),
                AppointmentScheduleEventTypeSetupResponse(
                    id = otherAppointmentScheduleEventTypeGeneric.id,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20,
                    providerType = AppointmentScheduleEventTypeLocation.ON_SITE,
                    isSelected = false,
                    isMultiProfessionalReferral = true,
                    title = "Consulta",
                    category = otherAppointmentScheduleEventTypeGeneric.category,
                )
            )
        )

        coEvery {
            service.getByIds(listOf(appointmentScheduleEventType.id, otherAppointmentScheduleEventTypeGeneric.id))
        } returns listOf(appointmentScheduleEventType, otherAppointmentScheduleEventTypeGeneric)

        coEvery {
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventTypeGeneric.id)
        } returns listOf(eventTypeProviderUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=${appointmentScheduleEventType.id},${otherAppointmentScheduleEventTypeGeneric.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            service.getByIds(any())
        }
        coVerify(exactly = 2) {
            eventTypeProviderUnitService.getForEventType(any())
        }

    }

    @ParameterizedTest
    @MethodSource("getAppointmentScheduleEventTypeSetupParams")
    fun `#webviewSetup should return appointment schedule event type setup`(
        staffId: UUID?,
        remoteEventType: AppointmentScheduleEventType?,
        onSiteEventType: AppointmentScheduleEventType?,
        eventTypeIds: List<UUID>,
        eventTypeProviderUnit: EventTypeProviderUnit,
        expected: AppointmentScheduleEventTypesSetupResponse
    ) {
        coEvery { service.getByIds(eventTypeIds) } returns listOfNotNull(remoteEventType, onSiteEventType)

        if (remoteEventType != null) {
            coEvery {
                eventTypeProviderUnitService.getForEventType(remoteEventType.id)
            } returns listOf(eventTypeProviderUnit.copy(providerUnitId = null))
        }
        if (onSiteEventType != null && staffId != null) {
            coEvery {
                eventTypeProviderUnitService.getForEventType(onSiteEventType.id)
            } returns listOf(eventTypeProviderUnit)
        }
        if (staffId != null) {
            coEvery { staffService.get(staffId) } returns staff.copy(id = staffId)
        }

        authenticatedAs(token, person) {
            val eventTypeParams = eventTypeIds.joinToString(",")
            val staffIdParam = staffId?.let { "&staff_id=$staffId" }.orEmpty()
            val url =
                "/appointment_schedule_event_type/setup?appointment_schedule_event_type_ids=$eventTypeParams$staffIdParam"
            get(url) { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        val verifyProviderUnitEventType = if (onSiteEventType != null && staffId != null) 2 else 1

        coVerifyOnce {
            service.getByIds(any())
            staffId?.let {
                staffService.get(any())
            }
        }
        coVerify(exactly = verifyProviderUnitEventType) {
            eventTypeProviderUnitService.getForEventType(any())
        }

    }

    @Test
    fun `#getEventProviderUnits should return staff provider units`() {
        val expectedResponse = EventProviderUnitResponse(
            providerUnits = listOf(
                ProviderUnitSetupResponse(
                    id = providerUnitId,
                    name = providerUnit.name,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20
                )
            )

        )

        coEvery {
            service.get(otherAppointmentScheduleEventType.id)
        } returns otherAppointmentScheduleEventType

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)


        coEvery {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                otherAppointmentScheduleEventType.id,
                staff.id
            )
        } returns listOf(providerUnit)

        coEvery { staffService.get(staff.id) } returns staff

        coEvery {
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(
                person.id,
                staff.id,
                appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling
            )
        } returns null.success()

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/provider_units?appointment_schedule_event_type_id=${otherAppointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(any(), any(), any())
            staffService.get(any())
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(any(), any(), any())
            eventTypeProviderUnitService.getForEventType(any())
            service.get(any())
        }

        coVerifyNone {
            providerUnitService.getByIds(any(), any())
        }
    }

    @Test
    fun `#getEventProviderUnits should generic event provider units`() {
        val expectedResponse = EventProviderUnitResponse(
            providerUnits = listOf(
                ProviderUnitSetupResponse(
                    id = providerUnitId,
                    name = providerUnit.name,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20
                )
            )
        )

        coEvery {
            service.get(otherAppointmentScheduleEventTypeGeneric.id)
        } returns otherAppointmentScheduleEventTypeGeneric

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventTypeGeneric.id)
        } returns listOf(eventTypeProviderUnit.copy(appointmentScheduleEventTypeId = otherAppointmentScheduleEventTypeGeneric.id))

        coEvery {
            providerUnitService.getByIds(listOf(providerUnit.id), false)
        } returns listOf(providerUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/provider_units?appointment_schedule_event_type_id=${otherAppointmentScheduleEventTypeGeneric.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            eventTypeProviderUnitService.getForEventType(any())
            service.get(any())
            providerUnitService.getByIds(any(), any())
        }

        coVerifyNone {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(any(), any(), any())
            staffService.get(any())
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(any(), any(), any())
        }
    }

    @Test
    fun `#getEventProviderUnits should return staff provider units and vacation events provider units if staff is on vacation period`() {
        val vacationEventType = appointmentScheduleEventType.copy(id = RangeUUID.generate())
        val vacationProviderUnit = providerUnit.copy(id = RangeUUID.generate(), name = "Provider vacation")
        val vacationEventProviderUnit =
            eventTypeProviderUnit.copy(id = RangeUUID.generate(), providerUnitId = vacationProviderUnit.id)
        val expectedResponse = EventProviderUnitResponse(
            providerUnits = listOf(
                ProviderUnitSetupResponse(
                    id = providerUnitId,
                    name = providerUnit.name,
                    duration = 20,
                    numberOfDaysFromNowToAllowScheduling = 20
                ),
                ProviderUnitSetupResponse(
                    id = vacationProviderUnit.id,
                    name = vacationProviderUnit.name,
                    duration = vacationEventProviderUnit.duration,
                    numberOfDaysFromNowToAllowScheduling = vacationEventProviderUnit.numberOfDaysFromNowToAllowScheduling
                )
            )
        )

        coEvery {
            service.get(otherAppointmentScheduleEventType.id)
        } returns otherAppointmentScheduleEventType

        coEvery {
            eventTypeProviderUnitService.getForEventType(otherAppointmentScheduleEventType.id)
        } returns listOf(eventTypeProviderUnit)


        coEvery {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
                person.id,
                otherAppointmentScheduleEventType.id,
                staff.id
            )
        } returns listOf(providerUnit)

        coEvery { staffService.get(staff.id) } returns staff

        coEvery {
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(
                person.id,
                staff.id,
                appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling
            )
        } returns vacationEventType.id.success()

        coEvery {
            service.get(vacationEventType.id)
        } returns vacationEventType

        coEvery {
            eventTypeProviderUnitService.getForEventType(vacationEventType.id)
        } returns listOf(vacationEventProviderUnit, eventTypeProviderUnit) // test distinct

        coEvery {
            providerUnitService.getByIds(listOf(vacationProviderUnit.id, providerUnit.id), false)
        } returns listOf(vacationProviderUnit, providerUnit)

        authenticatedAs(token, person) {
            get(
                "/appointment_schedule_event_type/provider_units?appointment_schedule_event_type_id=${otherAppointmentScheduleEventType.id}&staff_id=${staff.id}"
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(any(), any(), any())
            staffService.get(any())
            internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(any(), any(), any())
            providerUnitService.getByIds(any(), any())
        }
        coVerify(exactly = 2) {
            eventTypeProviderUnitService.getForEventType(any())
            service.get(any())
        }
    }

}
