package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.action.plan.model.ActionPlanTaskDetails
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.labs
import br.com.alice.common.RangeUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.member.api.models.ActionPlanItemResponseStatus
import br.com.alice.member.api.models.FrequencyTransport
import br.com.alice.member.api.models.HealthPlanItemDrawer
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemNavigationTag
import br.com.alice.member.api.models.HealthPlanItemNavigationTagColor
import br.com.alice.member.api.models.HealthPlanPresentationFields
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import br.com.alice.member.api.models.v2.ActionPlanTaskItemDetailResponse
import br.com.alice.member.api.services.TestCodesService
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.stream.Stream
import kotlin.test.BeforeTest

class SingleActionPlanTaskDetailsResponseConverterTest {

    companion object {

        private val dueDate = LocalDate.now().plusDays(1)
        private val staff = TestModelFactory.buildStaff()
        private val person = TestModelFactory.buildPerson()
        private val memberTier = ReferralMemberTier(
            isInMemberTier = false,
            memberProduct = ReferralMemberProduct(
                id = RangeUUID.generate(),
                name = "Alice Plan"
            ),
            suggestedReason = ReferralSuggestedSpecialistReason(
                reason = ReferralSuggestedSpecialistReasonType.OTHER,
                description = "porque o membro pediu"
            )
        )

        private val group = HealthPlanTaskGroupTransport(
            id = RangeUUID.generate(),
            personId = person.id,
            name = "Teste",
            healthPlanId = RangeUUID.generate(),
            descriptions = listOf(
                HealthPlanTaskGroupDescription(
                    description = "Teste",
                    type = HealthPlanTaskType.TEST_REQUEST
                )
            ),
        )

        private val suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            memberTier = memberTier
        )

        private val actionPlanTask = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            dueDate = dueDate,
            title = "Nutricionista",
            description = "Para melhorar sua alimentação",
            status = ActionPlanTaskStatus.ACTIVE,
            deadline = Deadline(
                unit = PeriodUnit.WEEK,
                quantity = 1,
                date = LocalDateTime.of(2022, 2, 8, 0, 0, 0)
            ),
            start = StartType.IMMEDIATE,
            type = ActionPlanTaskType.REFERRAL,
            favorite = false,
            scheduledAt = null,
            staffId = staff.id,
            groupId = group.id
        )

        private val referralTask = ReferralNew(
            diagnosticHypothesis = "Lesão por esforço repetitivo",
            specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
            subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
            referenceLetterSentDate = null,
            task = actionPlanTask,
            sessionsQuantity = null
        )

        @JvmStatic
        private fun referralTaskScenarios() = Stream.of(
            Arguments.of(
                referralTask.copy(title = "Encaminhamento para Geriatra"),
                "Encaminhamento para Geriatra",
                "99.99.99"
            ),
            Arguments.of(
                referralTask.copy(title = "Encaminhamento para Geriatra"),
                "Encaminhamento para Geriatra",
                "1.0.0"
            ),
            Arguments.of(referralTask.copy(title = "Geriatra"), "Encaminhamento para Geriatra", "99.99.99"),
            Arguments.of(referralTask.copy(title = "Geriatra"), "Encaminhamento para Geriatra", "1.0.0"),
            Arguments.of(
                referralTask.copy(title = "Encaminhamento para Geriatra").setSuggestedSpecialist(suggestedSpecialist),
                "Encaminhamento para Geriatra Caio",
                "99.99.99"
            ),
            Arguments.of(
                referralTask.copy(title = "Encaminhamento para Geriatra").setSuggestedSpecialist(suggestedSpecialist),
                "Encaminhamento para Geriatra Caio",
                "1.0.0"
            ),
            Arguments.of(
                referralTask.copy(title = "Geriatra").setSuggestedSpecialist(suggestedSpecialist),
                "Encaminhamento para Geriatra Caio",
                "99.99.99"
            ),
            Arguments.of(
                referralTask.copy(title = "Geriatra").setSuggestedSpecialist(suggestedSpecialist),
                "Encaminhamento para Geriatra Caio",
                "1.0.0"
            ),
        )
    }

    private val actionPlanTaskPresentationBuilder: ActionPlanTaskPresentationBuilder = mockk()
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService = mockk()
    private val testCodesService: TestCodesService = mockk()
    private val deviceService: DeviceService = mockk()
    private val singleActionPlanTaskDetailsResponseConverter = SingleActionPlanTaskDetailsResponseConverter(
        actionPlanTaskPresentationBuilder,
        healthPlanTaskGroupService,
        testCodesService,
        deviceService
    )

    private val referralTask = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
        subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = null
    )

    private val referralTaskWithMultipleSession = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", RangeUUID.generate()),
        subSpecialty = ReferralSpecialty("Joelho", RangeUUID.generate()),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = 2,
    )

    private val testRequestTask =
        TestRequestNew(
            task = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                title = "Exame de Sangue",
                description = "Diabetes e Colesterol",
                dueDate = dueDate,
                status = ActionPlanTaskStatus.ACTIVE,
                frequency = Frequency(
                    type = FrequencyType.QUANTITY_IN_PERIOD,
                    unit = PeriodUnit.HOUR,
                    quantity = 1,
                ),
                deadline = Deadline(
                    unit = PeriodUnit.WEEK,
                    quantity = 1,
                ),
                start = StartType.IF_HEADACHE,
                type = ActionPlanTaskType.TEST_REQUEST,
                favorite = false,
                scheduledAt = null,
                staffId = staff.id,
                groupId = group.id
            ),
            code = "1234"
        )

    private val genericTask = GenericTaskNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            title = "Exame de Sangue",
            description = "Diabetes e Colesterol",
            dueDate = dueDate,
            status = ActionPlanTaskStatus.ACTIVE,
            frequency = Frequency(
                type = FrequencyType.QUANTITY_IN_PERIOD,
                unit = PeriodUnit.HOUR,
                quantity = 1,
            ),
            deadline = Deadline(
                unit = PeriodUnit.WEEK,
                quantity = 1,
            ),
            start = StartType.IF_HEADACHE,
            type = ActionPlanTaskType.SLEEP,
            favorite = false,
            scheduledAt = null,
            staffId = staff.id,
        )
    )

    private val prescriptionTask = PrescriptionNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            title = "Exame de Sangue",
            description = "Diabetes e Colesterol",
            dueDate = dueDate,
            status = ActionPlanTaskStatus.ACTIVE,
            frequency = Frequency(
                type = FrequencyType.QUANTITY_IN_PERIOD,
                unit = PeriodUnit.HOUR,
                quantity = 1
            ),
            deadline = Deadline(
                unit = PeriodUnit.WEEK,
                quantity = 1
            ),
            start = StartType.IF_HEADACHE,
            type = ActionPlanTaskType.SLEEP,
            favorite = false,
            scheduledAt = null,
            staffId = staff.id,
        ),
        dose = null,
        action = null,
        routeOfAdministration = null,
        medicine = null,
        packing = 60,
    )

    private val emergencyTask = EmergencyNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            title = "Caganeira extrema",
            description = "Diabetes e Colesterol",
            dueDate = dueDate,
            status = ActionPlanTaskStatus.ACTIVE,
            frequency = Frequency(
                type = FrequencyType.QUANTITY_IN_PERIOD,
                unit = PeriodUnit.HOUR,
                quantity = 1,
            ),
            deadline = Deadline(
                unit = PeriodUnit.WEEK,
                quantity = 1,
            ),
            start = StartType.IF_HEADACHE,
            type = ActionPlanTaskType.EMERGENCY,
            favorite = false,
            scheduledAt = null,
            staffId = staff.id,
        ),
        diagnosticHypothesis = "Intolerância a Lactose"
    )

    private val taskDetails = ActionPlanTaskDetails(
        task = listOf(genericTask),
        staffs = listOf(staff),
        city = null,
    )

    private val preparations = emptyMap<String, TestPreparation>()

    @BeforeTest
    fun setup() {
        coEvery { healthPlanTaskGroupService.get(group.id!!) } returns group.success()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<ReferralNew>(),
                any(),
                any(),
            )
        } returns HealthPlanPresentationFields()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<QuestionnaireNew>(),
                any(),
            )
        } returns HealthPlanPresentationFields()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<TestRequestNew>(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        } returns HealthPlanPresentationFields()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<PrescriptionNew>(),
                any()
            )
        } returns HealthPlanPresentationFields(
            drawers = listOf(
                HealthPlanItemDrawer(
                    id = "preparation_drawer",
                    title = "Preparo",
                    subTitle = "Preparo para exame",
                    icon = "clipboard",
                    navigation = RemoteAction.labs(),
                )
            )
        )
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<PrescriptionNew>(),
                any()
            )
        } returns HealthPlanPresentationFields()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<EmergencyNew>(),
                any(),
            )
        } returns HealthPlanPresentationFields()
        coEvery {
            actionPlanTaskPresentationBuilder.getPresentationFields(
                any<GenericTaskNew>(),
                any(),
            )
        } returns HealthPlanPresentationFields()
        coEvery { testCodesService.getPreparations(any()) } returns preparations
        coEvery { deviceService.getDeviceByPerson(person.id.toString()) } returns TestModelFactory.buildDevice(
            deviceId = "123",
            appVersion = "3.2.0"
        ).success()
    }

    @ParameterizedTest(name = "convert converts test tasks when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts test tasks when redesign min version is {0}`(redesignVersion: String): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
            val result = singleActionPlanTaskDetailsResponseConverter.convert(
                taskDetails.copy(task = listOf(testRequestTask)),
                isUncoordinatedCoPayment = true
            )
            assertThat(result.healthPlanTaskId).isEqualTo(testRequestTask.id)
            assertThat(result.description).isEqualTo(group.name)
            assertThat(result.content).isEqualTo(group.descriptions.joinToString("\n") { it.description })
            assertThat(result.mainActionNavigation).isNull()
        }
    }

    @ParameterizedTest
    @MethodSource("referralTaskScenarios")
    fun `#convert converts referral active task`(
        task: ActionPlanTask,
        expectedDescription: String,
        redesignVersion: String
    ): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
            val result = singleActionPlanTaskDetailsResponseConverter.convert(
                taskDetails.copy(task = listOf(task.specialize<ReferralNew>())),
                isUncoordinatedCoPayment = true
            )
            assertThat(result.healthPlanTaskId).isEqualTo(task.id)
            assertThat(result.description).isEqualTo(expectedDescription)
            assertThat(result.content).isEqualTo(task.description)
            assertThat(result.mainActionNavigation).isNull()
        }
    }


    @ParameterizedTest(name = "convert converts referral active task with multiple sessions when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts referral active task with multiple sessions when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(referralTaskWithMultipleSession)),
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(referralTaskWithMultipleSession.id)
                assertThat(result.description)
                    .isEqualTo("Encaminhamento para ${referralTaskWithMultipleSession.title}")
                assertThat(result.content).isEqualTo(referralTaskWithMultipleSession.description)
                assertThat(result.mainActionNavigation).isNull()
            }
        }


    @ParameterizedTest(name = "convert converts referral internal scheduled task when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts referral internal scheduled task when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val scheduledActionPlan = actionPlanTask.copy(status = ActionPlanTaskStatus.SCHEDULED)
                val scheduledReferralTask = referralTask.copy(task = scheduledActionPlan)
                val scheduledTaskDetails = taskDetails.copy(task = listOf(scheduledReferralTask))
                val healthPlanPresentationFields = HealthPlanPresentationFields(hideScheduleOptions = true)

                coEvery {
                    actionPlanTaskPresentationBuilder.getPresentationFields(any<ReferralNew>(), any(), any())
                } returns healthPlanPresentationFields

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    scheduledTaskDetails,
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(referralTask.id)
                assertThat(result.description).isEqualTo("Encaminhamento para ${referralTask.title}")
                assertThat(result.content).isEqualTo(referralTask.description)
                assertThat(result.mainActionNavigation).isNull()
            }
        }

    @ParameterizedTest(name = "convert converts referral external scheduled task when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts referral external scheduled task when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val scheduledActionPlan = actionPlanTask.copy(status = ActionPlanTaskStatus.SCHEDULED)
                val scheduledReferralTask = referralTask.copy(task = scheduledActionPlan)
                val scheduledTaskDetails = taskDetails.copy(task = listOf(scheduledReferralTask))

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    scheduledTaskDetails,
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(referralTask.id)
                assertThat(result.description).isEqualTo("Encaminhamento para ${referralTask.title}")
                assertThat(result.content).isEqualTo(referralTask.description)
                assertThat(result.mainActionNavigation).isNull()
            }
        }

    @ParameterizedTest(name = "convert converts referral external scheduled task with multiple sessions when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts referral external scheduled task with multiple sessions when redesign min version is {0}`(
        redesignVersion: String
    ): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val scheduledActionPlan =
                    actionPlanTask.copy(
                        status = ActionPlanTaskStatus.SCHEDULED,
                        content = mapOf("sessionsQuantity" to 2)
                    )
                val scheduledReferralTask = referralTaskWithMultipleSession.copy(task = scheduledActionPlan)
                val scheduledTaskDetails = taskDetails.copy(task = listOf(scheduledReferralTask))

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    scheduledTaskDetails,
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(referralTask.id)
                assertThat(result.description).isEqualTo("Encaminhamento para ${referralTask.title}")
                assertThat(result.content).isEqualTo(referralTask.description)
                assertThat(result.mainActionNavigation).isNull()
            }
        }

    @ParameterizedTest(name = "convert converts prescription task when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts prescription task when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(prescriptionTask)),
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(prescriptionTask.id)
                assertThat(result.description).isEqualTo(prescriptionTask.title)
                assertThat(result.content).isEqualTo(prescriptionTask.description)
                assertThat(result.mainActionNavigation).isNull()
            }
        }

    @ParameterizedTest(name = "convert converts emergency task when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts emergency task when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(emergencyTask)),
                    isUncoordinatedCoPayment = true
                )
                assertThat(result.healthPlanTaskId).isEqualTo(emergencyTask.id)
                assertThat(result.description).isEqualTo(emergencyTask.title + " - Pronto Socorro")
                assertThat(result.content).isEqualTo(emergencyTask.description)
                assertThat(result.mainActionNavigation).isNull()

            }
        }

    @ParameterizedTest(name = "convert converts generic task when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert converts generic task when redesign min version is {0}`(redesignVersion: String): Unit = runBlocking {
        withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
            val result = singleActionPlanTaskDetailsResponseConverter.convert(
                taskDetails,
                isUncoordinatedCoPayment = true
            )
            assertThat(result.healthPlanTaskId).isEqualTo(genericTask.id)
            assertThat(result.description).isEqualTo(genericTask.title)
            assertThat(result.content).isEqualTo(genericTask.description)
            assertThat(result.mainActionNavigation).isNull()
        }
    }

    @ParameterizedTest(name = "convert converts test request tasks without group when redesign min version is {0}")
    @CsvSource(
        "99.99.99,false",
        "1.0.0,true"
    )
    fun `#convert converts test request tasks without group when redesign min version is {0}`(
        redesignVersion: String,
        hideDeprecatedData: Boolean,
    ): Unit =
        mockLocalDateTime { localDateTime ->
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val testRequestWithoutGroup = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Exame de Sangue",
                    description = "Diabetes e Colesterol",
                    dueDate = dueDate,
                    status = ActionPlanTaskStatus.ACTIVE,
                    frequency = Frequency(
                        type = FrequencyType.QUANTITY_IN_PERIOD,
                        unit = PeriodUnit.HOUR,
                        quantity = 1,
                    ),
                    deadline = Deadline(
                        unit = PeriodUnit.WEEK,
                        quantity = 1,
                        date = localDateTime
                    ),
                    start = StartType.IMMEDIATE,
                    type = ActionPlanTaskType.TEST_REQUEST,
                    favorite = false,
                    staffId = staff.id,
                    date = localDateTime,
                    content = mapOf(
                        "code" to "1002030",
                        "memberGuidance" to "Conselhos para o exame de sangue do membro"
                    ),
                )

                val expectedResult = ActionPlanTaskItemDetailResponse(
                    section = testRequestWithoutGroup.type,
                    healthPlanTaskId = testRequestWithoutGroup.id,
                    canAskHealthTeam = false,
                    description = testRequestWithoutGroup.title!!,
                    content = testRequestWithoutGroup.specialize<TestRequestNew>().memberGuidance,
                    createdAt = testRequestWithoutGroup.createdAt,
                    frequency = takeIf { !hideDeprecatedData }?.let { FrequencyTransport("a cada 1 hora") },
                    deadline = null,
                    start = null,
                    status = ActionPlanItemResponseStatus.TO_DO,
                    staffInfo = TaskRequesterRequest(
                        staffId = staff.id,
                        name = staff.fullName,
                        profileImageUrl = staff.profileImageUrl,
                        requestedAt = testRequestWithoutGroup.releasedAt.toString(),
                        role = staff.role.description,
                    ),
                    favorite = testRequestWithoutGroup.favorite,
                    navigations = emptyList(),
                    hideScheduleOptions = null,
                    attachments = null,
                    warning = null,
                    appointmentScheduleId = null,
                )

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(testRequestWithoutGroup)),
                    isUncoordinatedCoPayment = true
                )

                assertThat(result).isEqualTo(expectedResult)
            }
        }

    @ParameterizedTest(name = "convert converts test request tasks without group with authorized authorization when redesign min version is {0}")
    @CsvSource(
        "99.99.99,false",
        "1.0.0,true"
    )
    fun `#convert converts test request tasks without group with authorized authorization when redesign min version is {0}`(
        redesignVersion: String,
        hideDeprecatedData: Boolean,
    ): Unit =
        mockLocalDateTime { localDateTime ->
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {

                val testRequestWithoutGroup = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Exame de Sangue",
                    dueDate = dueDate,
                    description = "Diabetes e Colesterol",
                    status = ActionPlanTaskStatus.ACTIVE,
                    frequency = Frequency(
                        type = FrequencyType.QUANTITY_IN_PERIOD,
                        unit = PeriodUnit.HOUR,
                        quantity = 1,
                    ),
                    deadline = Deadline(
                        unit = PeriodUnit.WEEK,
                        quantity = 1,
                        date = localDateTime
                    ),
                    start = StartType.IMMEDIATE,
                    type = ActionPlanTaskType.TEST_REQUEST,
                    favorite = false,
                    staffId = staff.id,
                    date = localDateTime,
                    content = mapOf(
                        "code" to "1002030",
                        "memberGuidance" to "Conselhos para o exame de sangue do membro"
                    )
                )

                val authorization = ActionPlanTaskAuthorization(
                    totvsGuiaStatus = TotvsGuiaStatus.AUTHORIZED,
                    totvsGuiaId = RangeUUID.generate()
                )

                val expectedResult = ActionPlanTaskItemDetailResponse(
                    section = testRequestWithoutGroup.type,
                    healthPlanTaskId = testRequestWithoutGroup.id,
                    canAskHealthTeam = false,
                    description = testRequestWithoutGroup.title!!,
                    content = testRequestWithoutGroup.specialize<TestRequestNew>().memberGuidance,
                    createdAt = testRequestWithoutGroup.createdAt,
                    frequency = takeIf { !hideDeprecatedData }?.let { FrequencyTransport("a cada 1 hora") },
                    deadline = null,
                    start = null,
                    status = ActionPlanItemResponseStatus.TO_DO,
                    staffInfo = TaskRequesterRequest(
                        staffId = staff.id,
                        name = staff.fullName,
                        profileImageUrl = staff.profileImageUrl,
                        requestedAt = testRequestWithoutGroup.releasedAt.toString(),
                        role = staff.role.description,
                    ),
                    favorite = testRequestWithoutGroup.favorite,
                    navigations = listOf(
                        HealthPlanItemNavigation(
                            name = "Autorização de procedimentos",
                            description = "Acompanhe os detalhes da sua guia e procedimentos",
                            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                            icon = "paper",
                            tag = HealthPlanItemNavigationTag(
                                icon = "check_outlined",
                                text = "Autorizada",
                                color = HealthPlanItemNavigationTagColor.GREEN
                            ),
                            navigation = NavigationResponse(
                                mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                                properties = mapOf("id" to authorization.totvsGuiaId!!)
                            ),
                        )
                    ),
                    hideScheduleOptions = null,
                    attachments = null,
                    warning = null,
                    appointmentScheduleId = null,
                )

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(testRequestWithoutGroup)),
                    isUncoordinatedCoPayment = true,
                    authorization
                )

                assertThat(result).isEqualTo(expectedResult)

            }
        }

    @ParameterizedTest(name = "convert converts test request tasks without group with pending authorization when redesign min version is {0}")
    @CsvSource(
        "99.99.99,false",
        "1.0.0,true"
    )
    fun `#convert converts test request tasks without group with pending authorization when redesign min version is {0}`(
        redesignVersion: String,
        hideDeprecatedData: Boolean,
    ): Unit =
        mockLocalDateTime { localDateTime ->
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val testRequestWithoutGroup = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Exame de Sangue",
                    description = "Diabetes e Colesterol",
                    dueDate = dueDate,
                    status = ActionPlanTaskStatus.ACTIVE,
                    frequency = Frequency(
                        type = FrequencyType.QUANTITY_IN_PERIOD,
                        unit = PeriodUnit.HOUR,
                        quantity = 1,
                    ),
                    deadline = Deadline(
                        unit = PeriodUnit.WEEK,
                        quantity = 1,
                        date = localDateTime
                    ),
                    start = StartType.IMMEDIATE,
                    type = ActionPlanTaskType.TEST_REQUEST,
                    favorite = false,
                    staffId = staff.id,
                    date = localDateTime,
                    content = mapOf(
                        "code" to "1002030",
                        "memberGuidance" to "Conselhos para o exame de sangue do membro"
                    )
                )

                val authorization = ActionPlanTaskAuthorization(
                    totvsGuiaStatus = TotvsGuiaStatus.PENDING,
                    totvsGuiaId = RangeUUID.generate()
                )

                val expectedResult = ActionPlanTaskItemDetailResponse(
                    section = testRequestWithoutGroup.type,
                    healthPlanTaskId = testRequestWithoutGroup.id,
                    canAskHealthTeam = false,
                    description = testRequestWithoutGroup.title!!,
                    content = testRequestWithoutGroup.specialize<TestRequestNew>().memberGuidance,
                    createdAt = testRequestWithoutGroup.createdAt,
                    frequency = takeIf { !hideDeprecatedData }?.let { FrequencyTransport("a cada 1 hora") },
                    deadline = null,
                    start = null,
                    status = ActionPlanItemResponseStatus.TO_DO,
                    staffInfo = TaskRequesterRequest(
                        staffId = staff.id,
                        name = staff.fullName,
                        profileImageUrl = staff.profileImageUrl,
                        requestedAt = testRequestWithoutGroup.releasedAt.toString(),
                        role = staff.role.description,
                    ),
                    favorite = testRequestWithoutGroup.favorite,
                    navigations = listOf(
                        HealthPlanItemNavigation(
                            name = "Autorização de procedimentos",
                            description = "Acompanhe os detalhes da sua guia e procedimentos",
                            imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                            icon = "paper",
                            tag = HealthPlanItemNavigationTag(
                                icon = "clock",
                                text = "Análise em andamento",
                                color = HealthPlanItemNavigationTagColor.YELLOW
                            ),
                            navigation = NavigationResponse(
                                mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                                properties = mapOf("id" to authorization.totvsGuiaId!!)
                            ),
                        )
                    ),
                    hideScheduleOptions = null,
                    attachments = null,
                    warning = null,
                    appointmentScheduleId = null,
                )

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(testRequestWithoutGroup)),
                    isUncoordinatedCoPayment = true,
                    authorization
                )

                assertThat(result).isEqualTo(expectedResult)

            }
        }

    @ParameterizedTest(name = "convert should convert QUESTIONNAIRE actionPlanTask when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert should convert QUESTIONNAIRE actionPlanTask when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val questionnaireTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Questionário de Saúde",
                    dueDate = dueDate,
                    description = "Responda o questionário para melhorar o seu atendimento",
                    status = ActionPlanTaskStatus.ACTIVE,
                    type = ActionPlanTaskType.QUESTIONNAIRE,
                    staffId = staff.id,
                    groupId = group.id,
                )

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(questionnaireTask)),
                    isUncoordinatedCoPayment = true
                )

                assertThat(result.healthPlanTaskId).isEqualTo(questionnaireTask.id)
                assertThat(result.description).isEqualTo(questionnaireTask.title)
                assertThat(result.content).isEqualTo(questionnaireTask.description)
                assertThat(result.finishTask).isNull()
                assertThat(result.deleteTask).isNull()
                assertThat(result.mainActionNavigation).isNull()
            }
        }

    @ParameterizedTest(name = "convert should convert FOLLOW_UP_REQUEST actionPlanTask when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert should convert FOLLOW_UP_REQUEST actionPlanTask when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val followUpRequestTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Retorno com John Doe",
                    description = "Tentar agendar consulta após tomar medicamento.",
                    dueDate = dueDate,
                    status = ActionPlanTaskStatus.ACTIVE,
                    type = ActionPlanTaskType.FOLLOW_UP_REQUEST,
                    staffId = staff.id,
                    groupId = group.id
                )

                coEvery {
                    actionPlanTaskPresentationBuilder.getPresentationFields(
                        followUpRequestTask.specialize<FollowUpRequestNew>(),
                        any(),
                    )
                } returns HealthPlanPresentationFields()

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(followUpRequestTask)),
                    isUncoordinatedCoPayment = true
                )

                assertThat(result.healthPlanTaskId).isEqualTo(followUpRequestTask.id)
                assertThat(result.description).isEqualTo(followUpRequestTask.title)
                assertThat(result.content).isEqualTo(followUpRequestTask.description)
                assertThat(result.finishTask).isNull()
                assertThat(result.deleteTask).isNull()
                assertThat(result.mainActionNavigation).isNull()
                coVerifyOnce {
                    actionPlanTaskPresentationBuilder.getPresentationFields(
                        any<FollowUpRequestNew>(),
                        any()
                    )
                }
            }
        }

    @ParameterizedTest(name = "convert should convert SURGERY_PRESCRIPTION actionPlanTask when redesign min version is {0}")
    @CsvSource(
        "99.99.99",
        "1.0.0"
    )
    fun `#convert should convert SURGERY_PRESCRIPTION actionPlanTask when redesign min version is {0}`(redesignVersion: String): Unit =
        runBlocking {
            withFeatureFlag(FeatureNamespace.ALICE_APP, "redesigned_health_plan_details_min_version", redesignVersion) {
                val surgeryPrescriptionRequestTask = TestModelFactory.buildActionPlanTask(
                    personId = person.id,
                    title = "Cirurgia",
                    dueDate = dueDate,
                    description = "Agendar cirurgia.",
                    status = ActionPlanTaskStatus.ACTIVE,
                    type = ActionPlanTaskType.SURGERY_PRESCRIPTION,
                    staffId = staff.id,
                    groupId = group.id
                )

                coEvery {
                    actionPlanTaskPresentationBuilder.getPresentationFields(
                        surgeryPrescriptionRequestTask.specialize<SurgeryPrescriptionNew>(),
                        any()
                    )
                } returns HealthPlanPresentationFields()

                val result = singleActionPlanTaskDetailsResponseConverter.convert(
                    taskDetails.copy(task = listOf(surgeryPrescriptionRequestTask)),
                    isUncoordinatedCoPayment = true
                )

                assertThat(result.healthPlanTaskId).isEqualTo(surgeryPrescriptionRequestTask.id)
                assertThat(result.description).isEqualTo(surgeryPrescriptionRequestTask.title)
                assertThat(result.content).isEqualTo(surgeryPrescriptionRequestTask.description)
                assertThat(result.finishTask).isNull()
                assertThat(result.deleteTask).isNull()
                assertThat(result.mainActionNavigation).isNull()

                coVerifyOnce {
                    actionPlanTaskPresentationBuilder.getPresentationFields(
                        any<SurgeryPrescriptionNew>(),
                        any()
                    )
                }
            }
        }
}

private fun ActionPlanTask.setSuggestedSpecialist(suggestedSpecialist: SuggestedSpecialist) = this.copy(
    content = this.content?.plus("suggestedSpecialist" to suggestedSpecialist)
)
