package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.returns
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.*
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.DELETE_BUTTON_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_GENERIC_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_REFERRAL_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.FINISH_BUTTON_TEST_REQUEST_INFO
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.NOT_SHOW_BUTTON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_NAME
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.WARN_SCHEDULE_NAVIGATION_NAME
import br.com.alice.member.api.extensions.toHealthPlanDrawer
import br.com.alice.member.api.extensions.toItemAttachment
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.aliceAgoraDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.preparationsDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.prescriptionDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.promotionDrawer
import br.com.alice.member.api.models.HealthPlanItemDrawer.Companion.renewDrawer
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemStatusTag
import br.com.alice.member.api.models.HealthPlanPresentationFields
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.ScheduleInfo
import br.com.alice.member.api.models.TestPreparationTransport
import br.com.alice.member.api.models.v2.ActionNavigation
import br.com.alice.member.api.models.v2.ActionNavigationConfirmationModal
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.AppointmentCoordinationService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ActionPlanTaskPresentationBuilderTest {

    private val renewProtocolId = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
    private val appointmentScheduleId = RangeUUID.generate()
    private val dueDate = LocalDate.of(2022, 2, 8)
    private val scheduleService: AppointmentScheduleService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val appointmentCoordinationService: AppointmentCoordinationService = mockk()
    private val staffService: StaffService = mockk()

    private val builder: ActionPlanTaskPresentationBuilder = ActionPlanTaskPresentationBuilder(
        scheduleService,
        medicalSpecialtyService,
        appointmentCoordinationService,
        staffService
    )

    private val person = TestModelFactory.buildPerson()

    private val attachment = Attachment(
        id = RangeUUID.generate(),
        fileName = "Anexo",
        type = "PDF",
    )

    private val actionPlanTask = TestModelFactory.buildActionPlanTask(
        id = RangeUUID.generate(),
        personId = person.id,
        dueDate = dueDate,
        title = "Nutricionista",
        description = "Para melhorar sua alimentação",
        status = ActionPlanTaskStatus.ACTIVE,
        deadline = Deadline(PeriodUnit.WEEK, 1, date = LocalDateTime.of(2022, 2, 8, 0, 0, 0)),
        start = StartType.IMMEDIATE,
        type = ActionPlanTaskType.REFERRAL,
        favorite = false,
        scheduledAt = null,
        attachments = listOf(attachment),
        appointmentScheduleId = appointmentScheduleId,
    )
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule()

    private val memberTier = ReferralMemberTier(
        isInMemberTier = false,
        memberProduct = ReferralMemberProduct(
            id = RangeUUID.generate(),
            name = "Alice Plan"
        ),
        suggestedReason = ReferralSuggestedSpecialistReason(
            reason = ReferralSuggestedSpecialistReasonType.OTHER,
            description = "porque o membro pediu"
        )
    )

    private val specialtyId = RangeUUID.generate()
    private val staffId = "943ef8c3-ec04-41a9-ae43-385fe101d600".toUUID()
    private val staffSpecialist = TestModelFactory.buildStaff(
        id = staffId,
        role = Role.COMMUNITY,
        type = StaffType.COMMUNITY_SPECIALIST
    )

    private val questionnaireTask = QuestionnaireNew(
        healthFormAnswerSource = HealthFormAnswerSource(
            id = RangeUUID.generate().toString(),
            type = HealthFormAnswerSourceType.APPOINTMENT,
        ),
        questionnaireKey = "test_quest",
        task = actionPlanTask,
    )

    private val referralTaskInternal = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = null
    )

    private val referralTask = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = staffId,
            type = SpecialistType.STAFF,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = null
    )

    private val referralTaskWithSuggestedSpecialistTypeStaff = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = null
    )

    private val referralTaskWithoutSuggestedSpecialist = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = null
    )

    private val referralTaskWithMultipleSession = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = specialtyId,
            type = SpecialistType.CASSI_SPECIALIST,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        sessionsQuantity = 2
    )

    private val referralTaskWithMultipleSessionActiveOnGoing = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.STAFF,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask.copy(
            status = ActionPlanTaskStatus.ACTIVE_ON_GOING,
            acknowledgedAt = LocalDateTime.now()
        ),
        sessionsQuantity = 2
    )

    private val referralTaskWithMultipleFollowUps = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = specialtyId,
            type = SpecialistType.CASSI_SPECIALIST,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask,
        followUpMaxQuantity = 2
    )

    private val referralTaskWithMultipleFollowUpsActiveOnGoing = ReferralNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        specialty = ReferralSpecialty("Ortopedia", specialtyId),
        subSpecialty = ReferralSpecialty("Joelho", specialtyId),
        suggestedSpecialist = SuggestedSpecialist(
            name = "Caio",
            id = RangeUUID.generate(),
            type = SpecialistType.CASSI_SPECIALIST,
            memberTier = memberTier
        ),
        referenceLetterSentDate = null,
        task = actionPlanTask.copy(
            status = ActionPlanTaskStatus.ACTIVE_ON_GOING,
            acknowledgedAt = LocalDateTime.now()
        ),
        followUpMaxQuantity = 2
    )

    private val testRequestTask =
        TestRequestNew(
            task = TestModelFactory.buildActionPlanTask(
                personId = person.id,
                title = "Exame de Sangue",
                description = "Diabetes e Colesterol",
                dueDate = dueDate,
                status = ActionPlanTaskStatus.ACTIVE,
                frequency = Frequency(FrequencyType.QUANTITY_IN_PERIOD, PeriodUnit.HOUR, 1),
                deadline = Deadline(PeriodUnit.WEEK, 1),
                start = StartType.IF_HEADACHE,
                type = ActionPlanTaskType.TEST_REQUEST,
                favorite = false,
                scheduledAt = null,
                attachments = listOf(attachment),
                appointmentScheduleId = appointmentScheduleId,
            ),
            code = "1234"
        )

    private val genericTask = GenericTaskNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            title = "Exame de Sangue",
            description = "Diabetes e Colesterol",
            dueDate = dueDate,
            status = ActionPlanTaskStatus.ACTIVE,
            frequency = Frequency(FrequencyType.QUANTITY_IN_PERIOD, PeriodUnit.HOUR, 1),
            deadline = Deadline(PeriodUnit.WEEK, 1),
            start = StartType.IF_HEADACHE,
            type = ActionPlanTaskType.SLEEP,
            favorite = false,
            scheduledAt = null,
            attachments = listOf(attachment),
            appointmentScheduleId = appointmentScheduleId,
        )
    )

    private val followUpRequestTask = FollowUpRequestNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            dueDate = dueDate,
            title = "Retorno com John Doe",
            description = "Agende seu retorno digital em 1 dia. Preferencialmente entre os dias 31 de outubro e 1 de dezembro.",
            status = ActionPlanTaskStatus.ACTIVE,
            type = ActionPlanTaskType.FOLLOW_UP_REQUEST,
            attachments = listOf(attachment),
            appointmentScheduleId = appointmentScheduleId,
        )
    )

    private val surgeryPrescriptionTask = SurgeryPrescriptionNew(
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            dueDate = dueDate,
            title = "Indicação de procedimento cirúrgico solicitado por Luiz Silva",
            description = "Membro ...próximos passos.",
            status = ActionPlanTaskStatus.ACTIVE,
            type = ActionPlanTaskType.SURGERY_PRESCRIPTION,
            attachments = listOf(attachment),
            appointmentScheduleId = appointmentScheduleId,
        )
    )

    private val prescriptionTask = PrescriptionNew(
        digitalPrescription = DigitalPrescription(
            link = "https://www.memed.com",
            digits = "1234",
            documentLink = "https://www.memed.com/doc",
        ),
        dose = Dose(
            quantity = 1.0f,
            unit = MedicineUnit.DROP
        ),
        action = ActionType.SUCK,
        routeOfAdministration = RouteOfAdministration.IN_EAR,
        medicine = PrescriptionMedicine(
            name = "Dipirona",
            concentration = "500mg",
            type = PrescriptionMedicineType.SIMPLE
        ),
        packing = 100,
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            dueDate = dueDate,
            title = "Prescrição de medicamentos",
            description = "Tomar 1 comprimido de 8 em 8 horas",
            status = ActionPlanTaskStatus.ACTIVE,
            type = ActionPlanTaskType.PRESCRIPTION,
            attachments = listOf(attachment),
            appointmentScheduleId = appointmentScheduleId,
        )
    )

    private val emergencyTask = EmergencyNew(
        diagnosticHypothesis = "Lesão por esforço repetitivo",
        task = TestModelFactory.buildActionPlanTask(
            id = RangeUUID.generate(),
            personId = person.id,
            dueDate = dueDate,
            title = "Emergência",
            description = "Procure atendimento médico imediatamente",
            status = ActionPlanTaskStatus.ACTIVE,
            type = ActionPlanTaskType.EMERGENCY,
            attachments = listOf(attachment),
            appointmentScheduleId = appointmentScheduleId,
        )
    )

    private val activeExternalReferralNavigation = listOf(
        HealthPlanItemNavigation(
            name = WARN_SCHEDULE_NAVIGATION_NAME,
            description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
            imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
            icon = WARN_SCHEDULE_NAVIGATION_ICON,
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                properties = mapOf(
                    "health_plan_task_id" to referralTask.id
                )
            ),
        )
    )

    private val activeExternalTestRequestNavigation = listOf(
        HealthPlanItemNavigation(
            name = WARN_SCHEDULE_NAVIGATION_NAME,
            description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
            imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
            icon = WARN_SCHEDULE_NAVIGATION_ICON,
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                properties = mapOf(
                    "health_plan_task_id" to testRequestTask.id
                )
            ),
        )
    )

    private val specialty = TestModelFactory.buildMedicalSpecialty()

    @BeforeTest
    fun setup() {
        coEvery { medicalSpecialtyService.getById(specialtyId) } returns specialty
    }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to questionnaire task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,",
        "true,false"
    )
    fun `#getPresentationFields should get presentation fields to questionnaire task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = null,
                navigations = null,
                scheduleInfo = null,
                hideScheduleOptions = false,
                expireDetails = null,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.questionnaireAction(questionnaireTask),
                attachments = takeIf { !isRedesign }?.let { questionnaireTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                questionnaireTask,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED external referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED external referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val scheduleExternalReferralNavigation = listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOf(
                            "health_plan_task_id" to referralTask.id,
                            "scheduled_at" to scheduledAt
                        )
                    )
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_REFERRAL_INFO },
                deleteTask = null,
                navigations = takeIf { !isRedesign }?.let { scheduleExternalReferralNavigation },
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                hideScheduleOptions = true,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralScheduleIsDoneAction(referralTask, false)
                },
                tertiaryActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )
            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns false

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    referralTaskCopy,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @Test
    fun `#getPresentationFields should get presentation fields to SCHEDULED external referral task without navigation for primary attention referrals`(): Unit =
        runBlocking {
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns specialty.copy(
                attentionLevel = AttentionLevel.PRIMARY
            )

            val scheduledAt = LocalDateTime.now()
            val referralTaskCopy = referralTaskWithoutSuggestedSpecialist.copy(
                task = referralTaskWithoutSuggestedSpecialist.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                finishTask = FINISH_BUTTON_REFERRAL_INFO,
                deleteTask = null,
                navigations = null,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                hideScheduleOptions = true,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = referralTask.attachments.toItemAttachment(),
                statusTag = null,
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTaskCopy.id)
            } returns false

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = false,
                isRedesignHealthPlanDetailsEnabled = false
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTask,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            }
        }


    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {

            val preparations = listOf(
                TestPreparationTransport(
                    title = "Preparo 1",
                    description = "jejum de 12 horas",
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let {
                    listOf(attachment.toHealthPlanDrawer(), preparationsDrawer(preparations), aliceAgoraDrawer())
                },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTask),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTask)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                testRequestTask,
                null,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign,
                preparations = preparations,
                authorization = null,
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task acknowledged yesterday isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task acknowledged yesterday isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = takeIf { !isRedesign }?.let { activeExternalReferralNavigation },
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val acknowledgedAtReferralTask = referralTask.copy(
                task = referralTask.task.copy(
                    acknowledgedAt = LocalDateTime.now().plusDays(2)
                )
            )
            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(
                    acknowledgedAtReferralTask.personId,
                    acknowledgedAtReferralTask.id
                )
            } returns emptyList()

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    acknowledgedAtReferralTask,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE test request task acknowledged yesterday isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE test request task acknowledged yesterday isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val acknowledgedAtTestRequestTask = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    acknowledgedAt = LocalDateTime.now().plusDays(2)
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = takeIf { !isRedesign }?.let { activeExternalTestRequestNavigation },
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTask),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTask)
                },
                attachments = takeIf { !isRedesign }?.let { acknowledgedAtTestRequestTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result =
                    builder.getPresentationFields(
                        acknowledgedAtTestRequestTask,
                        null,
                        isUncoordinatedCoPayment = true,
                        isRedesignHealthPlanDetailsEnabled = isRedesign,
                        preparations = emptyList(),
                        authorization = null,
                    )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED internal referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED internal referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        mockLocalDateTime { localDateTimeNow ->
            val referralTaskCopy = referralTaskInternal.copy(
                status = ActionPlanTaskStatus.SCHEDULED,
                scheduledAt = localDateTimeNow
            ).specialize<ReferralNew>()

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                navigations = null,
                scheduleInfo =
                takeIf { !isRedesign }?.let {
                    ScheduleInfo(
                        scheduledAt = localDateTimeNow,
                        details = "Agendado"
                    )
                },
                hideScheduleOptions = true,
                expireDetails = null,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { referralTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
                appointmentScheduleId = takeIf { isRedesign }?.let { referralTaskCopy.appointmentScheduleId!!.toString() }
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskCopy.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(appointmentSchedule)
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()


            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTaskCopy.id)
            } returns true

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED external test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED external test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val scheduleExternalTestRequestNavigation = listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOf(
                            "health_plan_task_id" to testRequestTask.id,
                            "scheduled_at" to scheduledAt
                        )
                    ),
                )
            )

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = null,
                navigations = takeIf { !isRedesign }?.let { scheduleExternalTestRequestNavigation },
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTaskCopy)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTaskCopy.id)
            } returns false

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    testRequestTaskCopy,
                    null,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign,
                    preparations = emptyList(),
                    authorization = null,
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED internal test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED internal test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        mockLocalDateTime { localDateTimeNow ->
            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = localDateTimeNow
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                navigations = null,
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = localDateTimeNow,
                    details = "Agendado"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTaskCopy)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTaskCopy.id)
            } returns true

            val result = builder.getPresentationFields(
                testRequestTaskCopy,
                null,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign,
                preparations = emptyList(),
                authorization = null,
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to generic task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,",
        "true,true"
    )
    fun `#getPresentationFields should get presentation fields to generic task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        canArchive: Boolean?,
    ): Unit = runBlocking {
        val expectedPresentationFields = HealthPlanPresentationFields(
            canAskHealthTeam = !isRedesign,
            finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
            deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
            navigations = null,
            expireDetails = null,
            drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
            canArchive = canArchive,
            archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
            mainActionNavigation = takeIf { isRedesign }?.let { ActionNavigation.genericIsDoneAction(genericTask) },
            attachments = takeIf { !isRedesign }?.let { genericTask.attachments.toItemAttachment() },
            statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
        )

        coEvery {
            scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
        } returns true

        val result = builder.getPresentationFields(genericTask, isRedesign)

        assertThat(result).isEqualTo(expectedPresentationFields)
    }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task with multiple sessions but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task with multiple sessions but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                recurrentScheduleInfo = ScheduleInfo(
                    scheduledAt = null,
                    details = "Consulta 1 - Agendar até **08/02/22**"
                ),
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithMultipleSession.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns true

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            coEvery {
                staffService.get(referralTaskWithMultipleSessionActiveOnGoing.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()


            val result = builder.getPresentationFields(
                referralTaskWithMultipleSession,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }


    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE_ON_GOING referral task with multiple sessions but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true,true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE_ON_GOING referral task with multiple sessions but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = takeIf { !isRedesign }?.let { activeExternalReferralNavigation },
                hideScheduleOptions = false,
                recurrentScheduleInfo = ScheduleInfo(
                    scheduledAt = null,
                    details = "Consulta 1 - Agendar até **08/02/22**"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithMultipleFollowUpsActiveOnGoing.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()

            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()


            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns true

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            coEvery {
                staffService.get(referralTaskWithMultipleSessionActiveOnGoing.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    referralTaskWithMultipleSessionActiveOnGoing,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task with multiple follow ups but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task with multiple follow ups but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                recurrentScheduleInfo = ScheduleInfo(
                    scheduledAt = null,
                    details = "Consulta 1 - Agendar até **08/02/22**"
                ),
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithMultipleFollowUps.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns true

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result =
                builder.getPresentationFields(
                    referralTaskWithMultipleFollowUps,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE_ON_GOING referral task with multiple follow ups but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE_ON_GOING referral task with multiple follow ups but none scheduled or completed isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = takeIf { !isRedesign }?.let { activeExternalReferralNavigation },
                hideScheduleOptions = false,
                recurrentScheduleInfo = ScheduleInfo(
                    scheduledAt = null,
                    details = "Consulta 1 - Agendar até **08/02/22**"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithMultipleFollowUpsActiveOnGoing.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns true

            coEvery {
                staffService.get(referralTaskWithMultipleFollowUpsActiveOnGoing.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()


            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    referralTaskWithMultipleFollowUpsActiveOnGoing,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to DONE referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',false"
    )
    fun `#getPresentationFields should get presentation fields to DONE referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = null,
                navigations = null,
                hideScheduleOptions = true,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.doneTag() },
                canEditAppointmentSchedule = false,
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.DONE,
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to EXPIRED referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',false"
    )
    fun `#getPresentationFields should get presentation fields to EXPIRED referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = null,
                navigations = null,
                hideScheduleOptions = true,
                alert = takeIf { !isRedesign }?.let { taskExpiredAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.expiredTag() },
                canEditAppointmentSchedule = false,

                )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED,
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to OVERDUE referral task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to OVERDUE referral task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                alert = takeIf { !isRedesign }?.let { taskOverdueAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val referralTaskCopy = referralTaskWithoutSuggestedSpecialist.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.OVERDUE,
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithoutSuggestedSpecialist.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTaskWithoutSuggestedSpecialist.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED external grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED external grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            val taskList = listOf(testRequestTaskCopy)

            val scheduleExternalTestRequestNavigation = listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOf(
                            "health_plan_task_id" to testRequestTask.id,
                            "scheduled_at" to scheduledAt
                        )
                    ),
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                deleteTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                navigations = takeIf { !isRedesign }?.let { scheduleExternalTestRequestNavigation },
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneMultipleAction(taskList)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTaskCopy.id)
            } returns false

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result =
                    builder.getPresentationFields(
                        taskList,
                        null,
                        isUncoordinatedCoPayment = true,
                        isRedesignHealthPlanDetailsEnabled = isRedesign,
                        preparations = emptyList()
                    )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            val taskList = listOf(testRequestTaskCopy)

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                deleteTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                navigations = null,
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneMultipleAction(taskList)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTaskCopy.id)
            } returns true

            val result =
                builder.getPresentationFields(
                    taskList,
                    null,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to EXPIRED test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',false"
    )
    fun `#getPresentationFields should get presentation fields to EXPIRED test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED,
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                hideScheduleOptions = true,
                alert = takeIf { !isRedesign }?.let { taskExpiredAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTaskCopy)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.expiredTag() },
                warning = takeIf { isRedesign }?.let { "Essa tarefa expirou porque passou a data de agendamento do exame." },
            )

            val result = builder.getPresentationFields(
                testRequestTaskCopy,
                null,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign,
                preparations = emptyList(),
                authorization = null,
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to OVERDUE test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to OVERDUE test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.OVERDUE,
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                hideScheduleOptions = false,
                alert = takeIf { !isRedesign }?.let { taskOverdueAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTaskCopy),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneAction(testRequestTaskCopy)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                testRequestTaskCopy,
                null,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign,
                preparations = emptyList(),
                authorization = null,
            )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.ACTIVE,
                    scheduledAt = scheduledAt
                )
            )

            val taskList = listOf(testRequestTaskCopy)

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_TEST_REQUEST_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTaskCopy),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneMultipleAction(taskList)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result =
                builder.getPresentationFields(
                    taskList,
                    null,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to OVERDUE internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to OVERDUE internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.OVERDUE,
                    scheduledAt = scheduledAt
                )
            )

            val taskList = listOf(testRequestTaskCopy)


            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                navigations = null,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_TEST_REQUEST_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                hideScheduleOptions = false,
                alert = takeIf { !isRedesign }?.let { taskOverdueAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTaskCopy),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneMultipleAction(taskList)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )


            val result =
                builder.getPresentationFields(
                    taskList,
                    null,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to EXPIRED internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',false"
    )
    fun `#getPresentationFields should get presentation fields to EXPIRED internal grouped test request task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {

            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED,
                    scheduledAt = scheduledAt
                )
            )

            val taskList = listOf(testRequestTaskCopy)

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                hideScheduleOptions = true,
                alert = takeIf { !isRedesign }?.let { taskExpiredAlert },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.testRequestIsDoneMultipleAction(taskList)
                },
                attachments = takeIf { !isRedesign }?.let { testRequestTaskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.expiredTag() },
                warning = takeIf { isRedesign }?.let { "Essa tarefa expirou porque passou a data de agendamento do exame." },
            )

            val result =
                builder.getPresentationFields(
                    taskList,
                    null,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task without copay link and with correct expire details for primary attention referrals")
    @CsvSource(
        "false,",
        "true,'Agendar até 08/02/2022'"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task without copay link and with correct expire details for primary attention referrals`(
        isAdvancedAccess: Boolean,
        expectedExpireDetails: String?,
    ): Unit =
        runBlocking {
            val referralTask = referralTaskWithoutSuggestedSpecialist.copy(
                isAdvancedAccess = isAdvancedAccess,
            )

            coEvery { medicalSpecialtyService.getById(specialtyId) } returns specialty.copy(
                attentionLevel = AttentionLevel.PRIMARY
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = false,
                finishTask = null,
                deleteTask = null,
                navigations = null,
                hideScheduleOptions = false,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                attachments = null,
                statusTag = HealthPlanItemStatusTag.openTag(),
                drawers = listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()),
                expireDetails = expectedExpireDetails,
                canArchive = true,
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(
                    referralTask.personId,
                    referralTask.id
                )
            } returns emptyList()

            val result =
                builder.getPresentationFields(
                    referralTask,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = true
                )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task without copay link for therapy referrals when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task without copay link for therapy referrals isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            coEvery { medicalSpecialtyService.getById(specialtyId) } returns specialty.copy(
                isTherapy = true
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, true),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralScheduleIsDoneAction(
                        referralTask,
                        isTherapy = true
                    )
                },
                attachments = takeIf { !isRedesign }?.let { referralTaskWithoutSuggestedSpecialist.attachments.toItemAttachment() },

                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )


            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithoutSuggestedSpecialist.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(
                    referralTaskWithoutSuggestedSpecialist.personId,
                    referralTaskWithoutSuggestedSpecialist.id
                )
            } returns emptyList()

            val result =
                builder.getPresentationFields(
                    referralTaskWithoutSuggestedSpecialist,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

            assertThat(result).isEqualTo(expectedPresentationFields)

            coVerifyNone {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTaskWithoutSuggestedSpecialist.id)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral when referral is for secondary attention level isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral when referral is for secondary attention level isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            coEvery {
                medicalSpecialtyService.getById(specialtyId)
            } returns specialty.copy(
                isTherapy = true,
                attentionLevel = AttentionLevel.SECONDARY
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, true),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralScheduleIsDoneAction(referralTask, true)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(testRequestTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()


            val result = builder.getPresentationFields(
                referralTask,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral when referral is for secondary attention level isRedesignHealthPlanDetailsEnabled is {0} and suggestedSpecialist is type Staff")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral when referral is for secondary attention level isRedesignHealthPlanDetailsEnabled is {0} and suggestedSpecialist is type Staff`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            coEvery {
                medicalSpecialtyService.getById(specialtyId)
            } returns specialty.copy(
                isTherapy = true,
                attentionLevel = AttentionLevel.SECONDARY
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                deleteTask = takeIf { !isRedesign }?.let { NOT_SHOW_BUTTON },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(
                    referralTaskWithSuggestedSpecialistTypeStaff,
                    true
                ),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralScheduleIsDoneAction(referralTaskWithSuggestedSpecialistTypeStaff, true)
                },
                attachments = takeIf { !isRedesign }?.let { referralTaskWithSuggestedSpecialistTypeStaff.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val referralNew = referralTaskWithSuggestedSpecialistTypeStaff.copy(
                task = referralTaskWithSuggestedSpecialistTypeStaff.task.copy(
                    content = mapOf(
                        "specialty" to mapOf(
                            "id" to specialtyId,
                            "name" to "Ortopedia"
                        )
                    )
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskWithSuggestedSpecialistTypeStaff.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(testRequestTask.personId, referralNew.id)
            } returns emptyList()
            coEvery {
                staffService.get(referralNew.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralNew,
                isUncoordinatedCoPayment = true,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should remove copay info on ACTIVE referral task when is not uncoordinated co payment`(): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = false,
                finishTask = null,
                deleteTask = null,
                navigations = null,
                hideScheduleOptions = false,
                expireDetails = "Agendar até 08/02/2022",
                drawers = listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()),
                canArchive = true,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = ActionNavigation.referralAlreadyScheduledAction(referralTask),
                attachments = null,
                statusTag = HealthPlanItemStatusTag.openTag(),
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTask.personId, referralTask.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTask,
                isUncoordinatedCoPayment = false,
                isRedesignHealthPlanDetailsEnabled = true
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should remove expired alert to EXPIRED referral task when is not uncoordinated co payment`(): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                hideScheduleOptions = true,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = referralTask.attachments.toItemAttachment(),
                statusTag = null,
                canEditAppointmentSchedule = false,
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED,
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskCopy.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()
            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = false,
                isRedesignHealthPlanDetailsEnabled = false
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should remove expired alert to OVERDUE referral task when is not uncoordinated co payment`(): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                deleteTask = DELETE_BUTTON_INFO,
                hideScheduleOptions = false,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                attachments = referralTask.attachments.toItemAttachment(),
                statusTag = null,
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.OVERDUE,
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTaskCopy.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            val result = builder.getPresentationFields(
                referralTaskCopy,
                isUncoordinatedCoPayment = false,
                isRedesignHealthPlanDetailsEnabled = false
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should remove expired alert to EXPIRED test request task when is not uncoordinated co payment`(): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED,
                    scheduledAt = scheduledAt
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                hideScheduleOptions = true,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = testRequestTaskCopy.attachments.toItemAttachment(),
                statusTag = null,
            )

            val result =
                builder.getPresentationFields(
                    listOf(testRequestTaskCopy),
                    null,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = false,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should remove expired alert to OVERDUE test request task when is not uncoordinated co payment`(): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.OVERDUE,
                    scheduledAt = scheduledAt
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                navigations = null,
                finishTask = FINISH_BUTTON_TEST_REQUEST_INFO,
                deleteTask = DELETE_BUTTON_INFO,
                hideScheduleOptions = false,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.testRequestAction(authorization = null, testRequestTaskCopy),
                attachments = testRequestTaskCopy.attachments.toItemAttachment(),
                statusTag = null,
            )

            val result =
                builder.getPresentationFields(
                    listOf(testRequestTaskCopy),
                    null,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = false,
                    preparations = emptyList()
                )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should return finish button when schedule at is before today for test request`(): Unit =
        mockLocalDateTime { localDateTime ->
            val scheduledAt = localDateTime.minusDays(10)

            val scheduleExternalTestRequestNavigation = listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOf(
                            "health_plan_task_id" to testRequestTask.id,
                            "scheduled_at" to scheduledAt
                        )
                    ),
                )
            )

            val testRequestTaskCopy = testRequestTask.copy(
                task = testRequestTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                navigations = scheduleExternalTestRequestNavigation,
                finishTask = FINISH_BUTTON_GENERIC_INFO,
                deleteTask = null,
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = testRequestTask.attachments.toItemAttachment(),
                statusTag = null,
            )

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(testRequestTask.id)
            } returns false

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    testRequestTaskCopy,
                    null,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = false,
                    preparations = emptyList(),
                    authorization = null,
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }

            coVerifyOnce { scheduleService.hasWithScheduledStatusByHealthPlanTask(any()) }
        }

    @Test
    fun `#getPresentationFields should return finish button when schedule at is before today for referral`(): Unit =
        mockLocalDateTime { localDateTime ->
            val scheduledAt = localDateTime.minusDays(10)

            val scheduleExternalReferralNavigation = listOf(
                HealthPlanItemNavigation(
                    name = WARN_SCHEDULE_NAVIGATION_NAME,
                    description = WARN_SCHEDULE_NAVIGATION_DESCRIPTION,
                    imageUrl = WARN_SCHEDULE_NAVIGATION_IMAGE,
                    icon = WARN_SCHEDULE_NAVIGATION_ICON,
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOf(
                            "health_plan_task_id" to referralTask.id,
                            "scheduled_at" to scheduledAt
                        )
                    ),
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                navigations = scheduleExternalReferralNavigation,
                finishTask = FINISH_BUTTON_REFERRAL_INFO,
                deleteTask = null,
                hideScheduleOptions = true,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = referralTask.attachments.toItemAttachment(),
                statusTag = null,
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )

            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns listOf(appointmentSchedule)

            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns false

            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", true) {
                val result = builder.getPresentationFields(
                    referralTaskCopy,
                    isUncoordinatedCoPayment = false,
                    isRedesignHealthPlanDetailsEnabled = false
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
            }


            coVerifyOnce { scheduleService.hasWithScheduledStatusByHealthPlanTask(any()) }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED external referral task without navigation when FF is disabled isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED external referral task without navigation when FF is disabled isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val scheduledAt = LocalDateTime.now()

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_REFERRAL_INFO },
                deleteTask = null,
                navigations = null,
                scheduleInfo = ScheduleInfo(
                    scheduledAt = scheduledAt,
                    details = "Agendado"
                ),
                hideScheduleOptions = true,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralScheduleIsDoneAction(referralTask, false)
                },
                tertiaryActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
            )

            val referralTaskCopy = referralTask.copy(
                task = referralTask.task.copy(
                    status = ActionPlanTaskStatus.SCHEDULED,
                    scheduledAt = scheduledAt
                )
            )
            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(referralTaskCopy.personId, referralTaskCopy.id)
            } returns emptyList()

            coEvery {
                scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
            } returns false

            coEvery {
                staffService.get(referralTaskCopy.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", false) {
                val result = builder.getPresentationFields(
                    referralTaskCopy,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )
                assertThat(result).isEqualTo(expectedPresentationFields)
            }
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE referral task without navigation when FF is disabled isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE referral task without navigation when FF is disabled isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                hideScheduleOptions = false,
                copayInfo = takeIf { !isRedesign }?.let { copayLink },
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.referralScheduleAction(referralTask, false),
                secondaryActionNavigation = null,
                tertiaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.referralAlreadyScheduledAction(referralTask)
                },
                attachments = takeIf { !isRedesign }?.let { referralTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val acknowledgedAtReferralTask = referralTask.copy(
                task = referralTask.task.copy(
                    acknowledgedAt = LocalDateTime.now().plusDays(2)
                )
            )
            coEvery {
                scheduleService.findBy(
                    AppointmentScheduleFilter(
                        healthPlanTaskId = referralTask.id,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        sortOrder = SortOrder.Ascending
                    )
                )
            } returns emptyList()
            coEvery {
                appointmentCoordinationService.getByTaskId(
                    acknowledgedAtReferralTask.personId,
                    acknowledgedAtReferralTask.id
                )
            } returns emptyList()

            coEvery {
                staffService.get(acknowledgedAtReferralTask.suggestedSpecialist!!.id)
            } returns staffSpecialist.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "show_test_request_and_referral_navigation", false) {
                val result = builder.getPresentationFields(
                    acknowledgedAtReferralTask,
                    isUncoordinatedCoPayment = true,
                    isRedesignHealthPlanDetailsEnabled = isRedesign
                )

                assertThat(result).isEqualTo(expectedPresentationFields)
                coVerifyNone {
                    scheduleService.hasWithScheduledStatusByHealthPlanTask(referralTask.id)
                }
            }

        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE FOLLOW_UP_REQUEST task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE FOLLOW_UP_REQUEST task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = null,
                scheduleInfo = null,
                hideScheduleOptions = false,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.followUpAction(followUpRequestTask),
                attachments = takeIf { !isRedesign }?.let { followUpRequestTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                followUpRequestTask,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to SCHEDULED FOLLOW_UP_REQUEST task without an AppointmentSchedule when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,,true"
    )
    fun `#getPresentationFields should get presentation fields to SCHEDULED FOLLOW_UP_REQUEST task without an AppointmentSchedule isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val now = LocalDateTime.now()

            val taskCopy = followUpRequestTask.copy(
                status = ActionPlanTaskStatus.SCHEDULED,
                scheduledAt = now,
            ).specialize<FollowUpRequestNew>()

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_REFERRAL_INFO },
                deleteTask = null,
                scheduleInfo =
                takeIf { !isRedesign }?.let {
                    ScheduleInfo(
                        scheduledAt = now,
                        details = "Agendado"
                    )
                },
                hideScheduleOptions = false,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { taskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.scheduledTag() },
                appointmentScheduleId = takeIf { isRedesign }?.let { taskCopy.appointmentScheduleId!!.toString() },
            )


            val result = builder.getPresentationFields(
                taskCopy,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to ACTIVE SURGERY_PRESCRIPTION task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,",
        "true,true"
    )
    fun `#getPresentationFields should get presentation fields to ACTIVE SURGERY_PRESCRIPTION task`(
        isRedesign: Boolean,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                scheduleInfo = null,
                hideScheduleOptions = true,
                expireDetails = null,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                attachments = takeIf { !isRedesign }?.let { surgeryPrescriptionTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.ongoingTag() },
            )

            val result = builder.getPresentationFields(surgeryPrescriptionTask, isRedesign)

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @Test
    fun `#getPresentationFields should get presentation fields to EMERGENCY task when isUncoordinatedCoPayment is false`(): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = true,
                navigations = null,
                finishTask = FINISH_BUTTON_GENERIC_INFO,
                deleteTask = DELETE_BUTTON_INFO,
                scheduleInfo = null,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.emergencyAction(emergencyTask),
                attachments = emergencyTask.attachments.toItemAttachment(),
                statusTag = null,
            )

            val result = builder.getPresentationFields(
                emergencyTask,
                isRedesignHealthPlanDetailsEnabled = false
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to EMERGENCY task when isUncoordinatedCoPayment is true isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Válida até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to EMERGENCY task when isUncoordinatedCoPayment is true isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                navigations = null,
                scheduleInfo = null,
                expireDetails = expectedExpireDetails,
                drawers = takeIf { isRedesign }?.let { listOf(attachment.toHealthPlanDrawer(), aliceAgoraDrawer()) },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = ActionNavigation.emergencyAction(emergencyTask),
                secondaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.emergencyIsDoneAction(
                        emergencyTask
                    )
                },
                attachments = takeIf { !isRedesign }?.let { emergencyTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                emergencyTask,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to PRESCRIPTION task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',true"
    )
    fun `#getPresentationFields should get presentation fields to PRESCRIPTION task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = takeIf { !isRedesign }?.let { FINISH_BUTTON_GENERIC_INFO },
                deleteTask = takeIf { !isRedesign }?.let { DELETE_BUTTON_INFO },
                scheduleInfo = null,
                navigations = takeIf { !isRedesign }?.let {
                    listOf(
                        HealthPlanItemNavigation(
                            name = PRESCRIPTION_NAVIGATION_NAME,
                            description = ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_DESCRIPTION,
                            imageUrl = PRESCRIPTION_NAVIGATION_IMAGE,
                            icon = PRESCRIPTION_NAVIGATION_ICON,
                            navigation = NavigationResponse(
                                mobileRoute = MobileRouting.EXTERNAL_APP,
                                link = Link(
                                    href = prescriptionTask.digitalPrescription!!.link,
                                ),
                            ),
                        ),
                    )
                },
                expireDetails = null,
                drawers = takeIf { isRedesign }?.let {
                    listOf(
                        attachment.toHealthPlanDrawer(),
                        prescriptionDrawer(prescriptionTask),
                        renewDrawer(renewProtocolId),
                        promotionDrawer(),
                        aliceAgoraDrawer()
                    )
                },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.prescriptionIsDoneAction(prescriptionTask)
                },
                attachments = takeIf { !isRedesign }?.let { prescriptionTask.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.openTag() },
            )

            val result = builder.getPresentationFields(
                prescriptionTask,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }

    @ParameterizedTest(name = "getPresentationFields should get presentation fields to EXPIRED PRESCRIPTION task when isRedesignHealthPlanDetailsEnabled is {0}")
    @CsvSource(
        "false,,",
        "true,'Agendar até 08/02/2022',false"
    )
    fun `#getPresentationFields should get presentation fields to EXPIRED PRESCRIPTION task when isRedesignHealthPlanDetailsEnabled is {0}`(
        isRedesign: Boolean,
        expectedExpireDetails: String?,
        canArchive: Boolean?,
    ): Unit =
        runBlocking {
            val taskCopy = prescriptionTask.copy(
                task = prescriptionTask.task.copy(
                    status = ActionPlanTaskStatus.EXPIRED
                )
            )

            val expectedPresentationFields = HealthPlanPresentationFields(
                canAskHealthTeam = !isRedesign,
                finishTask = null,
                deleteTask = null,
                scheduleInfo = null,
                navigations = takeIf { !isRedesign }?.let {
                    listOf(
                        HealthPlanItemNavigation(
                            name = PRESCRIPTION_NAVIGATION_NAME,
                            description = ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_DESCRIPTION,
                            imageUrl = PRESCRIPTION_NAVIGATION_IMAGE,
                            icon = PRESCRIPTION_NAVIGATION_ICON,
                            navigation = NavigationResponse(
                                mobileRoute = MobileRouting.EXTERNAL_APP,
                                link = Link(
                                    href = taskCopy.digitalPrescription!!.link,
                                ),
                            ),
                        ),
                    )
                },
                expireDetails = null,
                drawers = takeIf { isRedesign }?.let {
                    listOf(
                        attachment.toHealthPlanDrawer(),
                        prescriptionDrawer(taskCopy),
                        renewDrawer(renewProtocolId),
                        promotionDrawer(),
                        aliceAgoraDrawer()
                    )
                },
                canArchive = canArchive,
                archiveConfirmationModal = ActionNavigationConfirmationModal.archiveConfirmation(),
                mainActionNavigation = null,
                secondaryActionNavigation = takeIf { isRedesign }?.let {
                    ActionNavigation.prescriptionIsDoneAction(taskCopy)
                },
                attachments = takeIf { !isRedesign }?.let { taskCopy.attachments.toItemAttachment() },
                statusTag = takeIf { isRedesign }?.let { HealthPlanItemStatusTag.expiredTag() },
                warning = takeIf { isRedesign }?.let { "Essa tarefa expirou porque a validade da receita venceu." },
            )

            val result = builder.getPresentationFields(
                taskCopy,
                isRedesignHealthPlanDetailsEnabled = isRedesign
            )

            assertThat(result).isEqualTo(expectedPresentationFields)
        }
}
