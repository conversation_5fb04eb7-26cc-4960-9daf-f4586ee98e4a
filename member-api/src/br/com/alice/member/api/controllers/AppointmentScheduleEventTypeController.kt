package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.EventTypeProviderUnit
import br.com.alice.data.layer.models.Staff
import br.com.alice.member.api.models.AppointmentScheduleEventTypeResponse
import br.com.alice.member.api.models.AppointmentScheduleEventTypeSetupResponse
import br.com.alice.member.api.models.AppointmentScheduleEventTypesSetupResponse
import br.com.alice.member.api.models.EventProviderUnitResponse
import br.com.alice.member.api.models.ProviderUnitSetupResponse
import br.com.alice.member.api.models.StaffResponse
import br.com.alice.member.api.models.StaffsRelatedToActiveConditionsResponse
import br.com.alice.member.api.models.currentAppSessionId
import br.com.alice.member.api.models.currentAppTimeZoneOffset
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.HealthConditionSchedulingService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID
import br.com.alice.member.api.services.AppointmentScheduleService as InternalAppointmentScheduleService

class AppointmentScheduleEventTypeController(
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val eventTypeProviderUnitService: EventTypeProviderUnitService,
    private val healthConditionSchedulingService: HealthConditionSchedulingService,
    private val staffService: StaffService,
    private val providerUnitService: ProviderUnitService,
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService,
    private val internalAppointmentScheduleService: InternalAppointmentScheduleService,
) : Controller() {

    suspend fun getByIdAndProviderUnit(appointmentScheduleEventTypeId: UUID, queryParams: Parameters): Response {
        val providerUnitId = queryParams["providerUnitId"]?.trim()?.lowercase().orEmpty()
        val providerUnitType = queryParams["providerUnitType"]?.trim()?.lowercase().orEmpty()
        val currentPersonId = currentUid()

        logger.info(
            "AppointmentScheduleEventTypeController::getByIdAndProviderUnit",
            "provider_unit_id" to providerUnitId,
            "provider_unit_type" to providerUnitType,
            "device_timezone_offset" to currentAppTimeZoneOffset(),
            "session_id" to currentAppSessionId(),
            "person_id" to currentPersonId
        )

        val isInvalidProviderUnitId = isInvalidProviderUnitId(providerUnitId)

        return appointmentScheduleEventTypeService
            .get(appointmentScheduleEventTypeId)
            .fold(
                { appointmentScheduleEventType ->
                    when {
                        isInvalidProviderUnitId -> {
                            if (providerUnitType.equals("digital", ignoreCase = true)) {
                                getEventTypeProviderUnit(
                                    appointmentScheduleEventType = appointmentScheduleEventType,
                                    providerUnitId = null
                                ).success()
                            } else {
                                appointmentScheduleEventType.convertTo(
                                    AppointmentScheduleEventTypeResponse::class
                                ).success()
                            }
                        }

                        else -> {
                            getEventTypeProviderUnit(
                                appointmentScheduleEventType = appointmentScheduleEventType,
                                providerUnitId = providerUnitId.toSafeUUID()
                            ).success()
                        }
                    }
                },
                {
                    logger.error(
                        "AppointmentScheduleEventTypeController::getByIdAndProviderUnit error",
                        "message" to it.message
                    )
                    it.failure()
                }
            )
            .foldResponse()
    }

    suspend fun webviewSetup(queryParams: Parameters): Response = coroutineScope {
        val currentPersonId = currentUid()
        val staffId = queryParams["staff_id"]?.trim()?.lowercase()?.toUUID()
        val appointmentScheduleEventTypeIds =
            queryParams["appointment_schedule_event_type_ids"]?.split(",")?.map { it.trim().lowercase().toUUID() }
        val appointmentScheduleId = queryParams["appointment_schedule_id"]?.trim()?.lowercase().orEmpty()
        val emptyResult = emptyList<AppointmentScheduleEventTypeSetupResponse>().success()

        logger.info(
            "AppointmentScheduleEventTypeController::webviewSetup",
            "staff_id" to staffId,
            "appointment_schedule_event_type_ids" to appointmentScheduleEventTypeIds,
            "appointment_schedule_id" to appointmentScheduleId,
            "person_id" to currentPersonId
        )

        val listAppointmentScheduleEventTypes = appointmentScheduleEventTypeIds?.let {
            appointmentScheduleEventTypeService.getByIds(it).get()
        }

        if (listAppointmentScheduleEventTypes == null) return@coroutineScope emptyResult.foldResponse()

        val staffDeferred = async { staffId?.let { getStaff(it) } }
        val appointmentScheduleEventTypes = listAppointmentScheduleEventTypes
            .mapNotNull { appointmentScheduleEventType ->

                if (!appointmentScheduleEventType.isMultiProfessionalReferral && staffId == null) return@mapNotNull null

                val eventTypeProviderUnits = getEventTypeProviderUnits(appointmentScheduleEventType.id)
                val providerType = getProviderType(eventTypeProviderUnits)

                val eventTypeProviderUnit =
                    eventTypeProviderUnits.find { it.providerUnitId == null } ?: eventTypeProviderUnits.first()

                val isSelected = isSelected(listAppointmentScheduleEventTypes, providerType)

                AppointmentScheduleEventTypeSetupResponse(
                    id = appointmentScheduleEventType.id,
                    duration = eventTypeProviderUnit.duration,
                    title = appointmentScheduleEventType.title,
                    numberOfDaysFromNowToAllowScheduling = eventTypeProviderUnit.numberOfDaysFromNowToAllowScheduling,
                    providerType = providerType,
                    isSelected = isSelected,
                    category = appointmentScheduleEventType.category,
                    isMultiProfessionalReferral = appointmentScheduleEventType.isMultiProfessionalReferral
                )
            }

        AppointmentScheduleEventTypesSetupResponse(
            events = sortEvents(appointmentScheduleEventTypes),
            staff = staffDeferred.await()
        ).success().foldResponse()
    }

    suspend fun getEventProviderUnits(queryParams: Parameters): Response {
        val currentPersonId = currentUid()
        val staffId = queryParams["staff_id"]?.trim()?.toUUID()
        val appointmentScheduleEventTypeId = queryParams["appointment_schedule_event_type_id"]?.trim()
            ?.toUUID()

        if (appointmentScheduleEventTypeId == null) {
            return Response(
                HttpStatusCode.BadRequest,
                message = "parameter appointment_schedule_event_type_id required"
            )
        }

        logger.info(
            "AppointmentScheduleEventTypeController::getEventProviderUnits",
            "staff_id" to staffId,
            "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
            "person_id" to currentPersonId
        )

        val staff = staffId?.let { getStaff(it) }

        return appointmentScheduleEventTypeId.let {
            appointmentScheduleEventTypeService.get(it).map { appointmentScheduleEventType ->
                getEventOnSiteProviderUnits(
                    appointmentScheduleEventType,
                    currentPersonId.toPersonId(),
                    staff,
                    getEventTypeProviderUnits(appointmentScheduleEventType.id),
                    appointmentScheduleEventType
                )
            }

        }.map {
            EventProviderUnitResponse(
                providerUnits = it
            )
        }.foldResponse()

    }

    private fun sortEvents(appointmentScheduleEventTypes: List<AppointmentScheduleEventTypeSetupResponse>?) =
        appointmentScheduleEventTypes?.filter { it.providerType == AppointmentScheduleEventTypeLocation.REMOTE }
            ?.let { remoteEvents ->
                appointmentScheduleEventTypes.filter { it.providerType != AppointmentScheduleEventTypeLocation.REMOTE }
                    .let { onSiteEvents ->
                        remoteEvents + onSiteEvents
                    }
            } ?: appointmentScheduleEventTypes


    private suspend fun getStaff(staffId: UUID) =
        staffService.get(staffId).map { staff ->
            StaffResponse(
                staff.id,
                staff.firstName,
                staff.lastName,
                staff.profileImageUrl,
                staff.role.name,
                staff.role.description
            )
        }.getOrNull()

    private fun getProviderType(eventTypeProviderUnits: List<EventTypeProviderUnit>) =
        eventTypeProviderUnits.let { eventTypeProviderUnit ->
            if (eventTypeProviderUnit.any { it.providerUnitId == null }) AppointmentScheduleEventTypeLocation.REMOTE else AppointmentScheduleEventTypeLocation.ON_SITE
        }

    private suspend fun getEventOnSiteProviderUnits(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        personId: PersonId,
        staff: StaffResponse?,
        eventProviderUnits: List<EventTypeProviderUnit>,
        currentEventType: AppointmentScheduleEventType
    ) = when {
        staff != null -> appointmentScheduleOptionService.getProviderUnitDetailsByStaffSchedule(
            personId,
            appointmentScheduleEventType.id,
            staff.id
        ).map { providerUnit ->
            providerUnit.map {
                val eventProviderUnit =
                    eventProviderUnits.find { eventProviderUnit -> eventProviderUnit.providerUnitId == it.id }
                ProviderUnitSetupResponse(
                    id = it.id,
                    name = it.name,
                    duration = eventProviderUnit?.duration ?: appointmentScheduleEventType.duration,
                    numberOfDaysFromNowToAllowScheduling = eventProviderUnit?.numberOfDaysFromNowToAllowScheduling
                        ?: appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling
                )
            }
        }.map { providerUnitList ->
            providerUnitList.plus(getProviderUnitsForVacationEvent(personId, staff.id, currentEventType)).distinct()
        }.getOrElse { emptyList() }

        appointmentScheduleEventType.isMultiProfessionalReferral -> getProviderUnitsResponse(
            eventProviderUnits,
            appointmentScheduleEventType
        )

        else -> emptyList()
    }

    private suspend fun getProviderUnitsForVacationEvent(
        personId: PersonId,
        staffId: UUID,
        currentEventType: AppointmentScheduleEventType
    ) =
        internalAppointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(
            personId,
            staffId,
            currentEventType.numberOfDaysFromNowToAllowScheduling
        ).fold(
            { vacationAppointmentScheduleEventTypeId ->
                vacationAppointmentScheduleEventTypeId?.let {
                    appointmentScheduleEventTypeService.get(vacationAppointmentScheduleEventTypeId)
                        .map { event -> getProviderUnitsResponse(getEventTypeProviderUnits(vacationAppointmentScheduleEventTypeId), event) }
                        .getOrElse { emptyList() }
                } ?: emptyList()
            },
            {
                logger.error(
                    "AppointmentScheduleEventTypeController::getProviderUnitsForVacationEvent error",
                    "message" to it.message
                )
                emptyList()
            }
        )

    private suspend fun getEventTypeProviderUnits(appointmentScheduleEventTypeId: UUID) =
        eventTypeProviderUnitService.getForEventType(appointmentScheduleEventTypeId).get()

    private suspend fun getProviderUnitsResponse(
        eventTypeProviderUnits: List<EventTypeProviderUnit>,
        appointmentScheduleEventType: AppointmentScheduleEventType,
    ) =
        eventTypeProviderUnits.mapNotNull { it.providerUnitId }.let { providerUnitIds ->
            if (providerUnitIds.isNotEmpty()) {
                providerUnitService.getByIds(providerUnitIds, withAddress = false).map {
                    it.map { providerUnit ->
                        val eventProviderUnit =
                            eventTypeProviderUnits.find { eventProviderUnit -> eventProviderUnit.providerUnitId == providerUnit.id }
                        ProviderUnitSetupResponse(
                            id = providerUnit.id,
                            name = providerUnit.name,
                            duration = eventProviderUnit?.duration ?: appointmentScheduleEventType.duration,
                            numberOfDaysFromNowToAllowScheduling = eventProviderUnit?.numberOfDaysFromNowToAllowScheduling
                                ?: appointmentScheduleEventType.numberOfDaysFromNowToAllowScheduling
                        )
                    }
                }.getOrNull()
            } else emptyList()
        }.orEmpty()

    private fun isSelected(
        listAppointmentScheduleEventTypes: List<AppointmentScheduleEventType>,
        providerType: AppointmentScheduleEventTypeLocation
    ) =
        when {
            listAppointmentScheduleEventTypes.size == 1 -> true
            else -> providerType == AppointmentScheduleEventTypeLocation.REMOTE
        }

    suspend fun getStaffsRelatedToActiveConditions(
        appointmentScheduleEventTypeId: UUID,
    ): Response {
        val currentPersonId = currentUid()

        logger.info(
            "AppointmentScheduleController::getStaffsRelatedToActiveConditions",
            "person_id" to currentPersonId,
            "appointment_schedule_event_id" to appointmentScheduleEventTypeId,
        )

        return healthConditionSchedulingService.getConditionsRelatedToEventTypeForScheduling(
            currentPersonId.toPersonId(),
            appointmentScheduleEventTypeId,
        ).flatMap { personCases ->
            val responsibleStaffIds = personCases.mapNotNull { it.responsibleStaffId }
            if (responsibleStaffIds.isEmpty()) {
                return@flatMap emptyList<Staff>().success()
            }
            staffService.findByList(responsibleStaffIds)
        }.map {
            StaffsRelatedToActiveConditionsResponse(
                it.map { staff ->
                    StaffResponse(
                        staff.id,
                        staff.firstName,
                        staff.lastName,
                        staff.profileImageUrl,
                        staff.role.name,
                        staff.role.description
                    )
                }
            )
        }
            .thenError { exception ->
                logger.error(
                    "AppointmentScheduleController::getStaffsRelatedToActiveConditions error",
                    "message" to exception.message,
                    exception
                )
            }
            .foldResponse()
    }

    private fun isInvalidProviderUnitId(providerUnitId: String) =
        providerUnitId.isEmpty() || providerUnitId.equals("undefined", ignoreCase = true)

    private suspend fun getEventTypeProviderUnit(
        appointmentScheduleEventType: AppointmentScheduleEventType,
        providerUnitId: UUID?
    ) =
        eventTypeProviderUnitService.getForEventType(appointmentScheduleEventType.id)
            .fold(
                { eventTypeProviderUnit ->
                    eventTypeProviderUnit.firstOrNull { it.providerUnitId == providerUnitId }
                        ?.let { filteredEventTypeProviderUnit ->
                            appointmentScheduleEventType.convertTo(
                                AppointmentScheduleEventTypeResponse::class
                            )
                                .copy(
                                    duration = filteredEventTypeProviderUnit.duration,
                                    numberOfDaysFromNowToAllowScheduling = filteredEventTypeProviderUnit.numberOfDaysFromNowToAllowScheduling
                                )
                        }
                        ?: appointmentScheduleEventType.convertTo(
                            AppointmentScheduleEventTypeResponse::class
                        )
                },
                { appointmentScheduleEventType.convertTo(AppointmentScheduleEventTypeResponse::class) }
            )

}
