package br.com.alice.member.api.controllers.duquesa

import br.com.alice.common.Response
import br.com.alice.common.toResponse
import br.com.alice.member.api.models.duquesa.AppointmentRule
import br.com.alice.member.api.models.duquesa.AppointmentRulesAction
import br.com.alice.member.api.models.duquesa.AppointmentRulesContent
import br.com.alice.member.api.models.duquesa.AppointmentRulesHeader

class DuquesaContentController : DuquesaController() {

    fun getAppointmentRulesContent(): Response =
        AppointmentRulesContent(
            header = AppointmentRulesHeader(
                title = "Veja como funciona a telemedicina",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/duquesa/appointment_rule_selfie.svg"
            ),
            appointmentRules = listOf(
                AppointmentRule(
                    icon = "team",
                    description = "Pessoas menores de 18 anos devem estar acompanhados do responsável."
                ),
                AppointmentRule(
                    icon = "id_card",
                    description = "Esteja com um documento de identificação com foto em mãos."
                ),
                AppointmentRule(
                    icon = "mic_on",
                    description = "Dê preferência por lugares reservados, com boa iluminação e silenciosos."
                ),
                AppointmentRule(
                    icon = "block",
                    description = "Não são permitidas consultas em lugares públicos ou no interior de veículos - seja parado ou em movimento."
                ),
            ),
            startAppointment = AppointmentRulesAction(
                buttonLabel = "Iniciar atendimento",
                manualLabel = "Ou se preferir, acesse pelo computador através do link abaixo:"
            )
        ).toResponse()
}
