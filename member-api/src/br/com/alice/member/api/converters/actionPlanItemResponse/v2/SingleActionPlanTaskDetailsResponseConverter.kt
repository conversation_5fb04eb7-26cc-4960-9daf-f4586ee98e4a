package br.com.alice.member.api.converters.actionPlanItemResponse.v2

import br.com.alice.action.plan.model.ActionPlanTaskDetails
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.coverage.client.City
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.EmergencyNew
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FollowUpRequestNew
import br.com.alice.data.layer.models.GenericTaskNew
import br.com.alice.data.layer.models.PrescriptionNew
import br.com.alice.data.layer.models.QuestionnaireNew
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.SurgeryPrescriptionNew
import br.com.alice.data.layer.models.TestRequestNew
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskGroupService
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.member.api.converters.FrequencyTransportConverter
import br.com.alice.member.api.extensions.toHealthPlanItemNavigation
import br.com.alice.member.api.models.ActionPlanItemResponseStatus
import br.com.alice.member.api.models.PreparationTransport
import br.com.alice.member.api.models.TestPreparationTransport
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import br.com.alice.member.api.models.v2.ActionPlanTaskItemDetailResponse
import br.com.alice.member.api.services.TestCodesService
import br.com.alice.membership.client.DeviceService
import java.time.LocalDateTime
import java.util.UUID

private const val PRESCRIPTION_INFO_MESSAGE_TEMPLATE =
    "Se você precisar acessar o PDF da receita, utilize o código de desbloqueio **%s**."

class SingleActionPlanTaskDetailsResponseConverter(
    private val actionPlanTaskPresentationBuilder: ActionPlanTaskPresentationBuilder,
    private val healthPlanTaskGroupService: HealthPlanTaskGroupService,
    private val testCodesService: TestCodesService,
    private val deviceService: DeviceService,
) {
    suspend fun convert(
        taskDetails: ActionPlanTaskDetails,
        isUncoordinatedCoPayment: Boolean,
        authorization: ActionPlanTaskAuthorization? = null,
    ): ActionPlanTaskItemDetailResponse {
        val task = taskDetails.task.first()

        val appVersion = SemanticVersion(
            deviceService.getDeviceByPerson(
                taskDetails.task[0].personId.toString()
            ).get().appVersion ?: "0.0.0"
        )

        val isRedesignHealthPlanDetailsEnabled = isRedesignHealthPlanDetailsEnabled(appVersion)

        return when (task.type) {
            ActionPlanTaskType.TEST_REQUEST -> {
                val group = task.groupId?.let { healthPlanTaskGroupService.get(it) }?.get()
                convertInTestRequestResponses(
                    task.specialize(),
                    isUncoordinatedCoPayment,
                    taskDetails.city,
                    taskDetails.staffs,
                    group,
                    getPreparations(taskDetails),
                    authorization,
                    isRedesignHealthPlanDetailsEnabled,
                )
            }

            ActionPlanTaskType.REFERRAL -> convertInReferralResponses(
                task.specialize(),
                isUncoordinatedCoPayment,
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled,
            )

            ActionPlanTaskType.PRESCRIPTION -> convertInPrescriptionResponses(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled
            )

            ActionPlanTaskType.EMERGENCY -> convertInEmergencyResponses(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled,
            )

            ActionPlanTaskType.QUESTIONNAIRE -> convertInQuestionnaireResponse(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled
            )

            ActionPlanTaskType.FOLLOW_UP_REQUEST -> convertInFollowUpRequestResponse(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled
            )

            ActionPlanTaskType.SURGERY_PRESCRIPTION -> convertInSurgeryPrescriptionResponse(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled
            )

            else -> convertInHealthHabitResponses(
                task.specialize(),
                taskDetails.staffs,
                isRedesignHealthPlanDetailsEnabled
            )
        }
    }

    private suspend fun convertInPrescriptionResponses(
        task: PrescriptionNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse {
        return actionPlanTaskPresentationBuilder.getPresentationFields(task, isRedesignHealthPlanDetailsEnabled)
            .let { presentationFields ->
                val infos = when {
                    task.hasDigitalPrescription() -> listOf(PRESCRIPTION_INFO_MESSAGE_TEMPLATE.format(task.digitalPrescription!!.digits))
                    else -> emptyList()
                }
                ActionPlanTaskItemDetailResponse(
                    section = task.type,
                    healthPlanTaskId = task.id,
                    canAskHealthTeam = presentationFields.canAskHealthTeam,
                    description = task.title ?: "",
                    content = task.description,
                    status = defineTaskStatus(task),
                    createdAt = task.createdAt,
                    frequency = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { buildFrequency(task) },
                    attachments = presentationFields.attachments,
                    infos = infos,
                    staffInfo = getStaffInfo(task, staffs),
                    favorite = task.favorite,
                    action = task.action?.description,
                    navigations = presentationFields.navigations,
                    finishTask = presentationFields.finishTask,
                    deleteTask = presentationFields.deleteTask,
                    mainActionNavigation = presentationFields.mainActionNavigation,
                    secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                    tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                    expireDetails = presentationFields.expireDetails,
                    drawers = presentationFields.drawers,
                    canArchive = presentationFields.canArchive,
                    archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                    statusTag = presentationFields.statusTag,
                    warning = presentationFields.warning,
                    appointmentScheduleId = presentationFields.appointmentScheduleId,
                    canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
                )
            }
    }

    private suspend fun convertInHealthHabitResponses(
        task: GenericTaskNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse {
        val presentationFields =
            actionPlanTaskPresentationBuilder.getPresentationFields(task, isRedesignHealthPlanDetailsEnabled)
        return ActionPlanTaskItemDetailResponse(
            section = task.type,
            healthPlanTaskId = task.id,
            canAskHealthTeam = presentationFields.canAskHealthTeam,
            description = task.title ?: "",
            content = task.description,
            createdAt = task.createdAt,
            frequency = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { buildFrequency(task) },
            status = defineTaskStatus(task),
            attachments = presentationFields.attachments,
            staffInfo = getStaffInfo(task, staffs),
            favorite = task.favorite,
            navigations = presentationFields.navigations,
            finishTask = presentationFields.finishTask,
            deleteTask = presentationFields.deleteTask,
            hideScheduleOptions = presentationFields.hideScheduleOptions,
            scheduleInfo = presentationFields.scheduleInfo,
            mainActionNavigation = presentationFields.mainActionNavigation,
            secondaryActionNavigation = presentationFields.secondaryActionNavigation,
            tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
            expireDetails = presentationFields.expireDetails,
            drawers = presentationFields.drawers,
            canArchive = presentationFields.canArchive,
            archiveConfirmationModal = presentationFields.archiveConfirmationModal,
            statusTag = presentationFields.statusTag,
            warning = presentationFields.warning,
            appointmentScheduleId = presentationFields.appointmentScheduleId,
            canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
        )
    }

    private suspend fun convertInEmergencyResponses(
        task: EmergencyNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(
            task,
            isRedesignHealthPlanDetailsEnabled
        )
            .let { presentationFields ->
                ActionPlanTaskItemDetailResponse(
                    section = task.type,
                    healthPlanTaskId = task.id,
                    canAskHealthTeam = presentationFields.canAskHealthTeam,
                    description = task.getViewDescription(),
                    content = task.description,
                    createdAt = task.createdAt,
                    frequency = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { buildFrequency(task) },
                    status = defineTaskStatus(task),
                    attachments = presentationFields.attachments,
                    staffInfo = getStaffInfo(task, staffs),
                    favorite = task.favorite,
                    navigations = presentationFields.navigations,
                    finishTask = presentationFields.finishTask,
                    deleteTask = presentationFields.deleteTask,
                    hideScheduleOptions = presentationFields.hideScheduleOptions,
                    scheduleInfo = presentationFields.scheduleInfo,
                    mainActionNavigation = presentationFields.mainActionNavigation,
                    secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                    tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                    expireDetails = presentationFields.expireDetails,
                    drawers = presentationFields.drawers,
                    canArchive = presentationFields.canArchive,
                    archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                    statusTag = presentationFields.statusTag,
                    warning = presentationFields.warning,
                    appointmentScheduleId = presentationFields.appointmentScheduleId,
                    canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
                )
            }

    private suspend fun convertInTestRequestResponses(
        task: TestRequestNew,
        isUncoordinatedCopPayment: Boolean,
        city: City?,
        staffs: List<Staff>,
        group: HealthPlanTaskGroupTransport?,
        preparations: List<TestPreparationTransport>,
        authorization: ActionPlanTaskAuthorization?,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(
            task,
            city,
            isUncoordinatedCopPayment,
            isRedesignHealthPlanDetailsEnabled,
            preparations,
            authorization,
        ).let { presentationFields ->
            ActionPlanTaskItemDetailResponse(
                section = task.type,
                healthPlanTaskId = task.id,
                canAskHealthTeam = presentationFields.canAskHealthTeam,
                description = group?.name ?: task.title ?: "",
                content = task.memberGuidance ?: group?.descriptions?.joinToString("\n") { it.description },
                createdAt = task.createdAt,
                frequency = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { buildFrequency(task) },
                status = defineTaskStatus(task),
                attachments = presentationFields.attachments,
                staffInfo = getStaffInfo(task, staffs),
                favorite = task.favorite,
                navigations = listOfNotNull(authorization?.toHealthPlanItemNavigation()) + presentationFields.navigations.orEmpty(),
                finishTask = presentationFields.finishTask,
                deleteTask = presentationFields.deleteTask,
                hideScheduleOptions = presentationFields.hideScheduleOptions,
                scheduleInfo = presentationFields.scheduleInfo,
                mainActionNavigation = presentationFields.mainActionNavigation,
                secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                preparation = if (preparations.isNotEmpty()) {
                    PreparationTransport(
                        warning = "Listamos abaixo os preparos de todos os exames e procedimentos que você irá agendar. **Siga sempre a orientação mais restrita** e, se tiver dúvidas, acesse o Alice Agora.",
                        preparations = preparations.toSet()
                    )
                } else null,
                taskAlert = presentationFields.alert,
                copayInfo = presentationFields.copayInfo,
                expireDetails = presentationFields.expireDetails,
                drawers = presentationFields.drawers,
                canArchive = presentationFields.canArchive,
                archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                statusTag = presentationFields.statusTag,
                warning = presentationFields.warning,
                appointmentScheduleId = presentationFields.appointmentScheduleId,
                canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
            )
        }

    private suspend fun convertInReferralResponses(
        task: ReferralNew,
        isUncoordinatedCopPayment: Boolean,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(
            task,
            isUncoordinatedCopPayment,
            isRedesignHealthPlanDetailsEnabled,
        ).let { presentationFields ->
            ActionPlanTaskItemDetailResponse(
                section = task.type,
                healthPlanTaskId = task.id,
                canAskHealthTeam = presentationFields.canAskHealthTeam,
                description = task.getViewDescription(),
                content = task.description,
                createdAt = task.createdAt,
                frequency = takeIf { !isRedesignHealthPlanDetailsEnabled }?.let { buildFrequency(task) },
                status = defineTaskStatus(task),
                attachments = presentationFields.attachments,
                staffInfo = getStaffInfo(task, staffs),
                favorite = task.favorite,
                navigations = presentationFields.navigations,
                finishTask = presentationFields.finishTask,
                deleteTask = presentationFields.deleteTask,
                hideScheduleOptions = presentationFields.hideScheduleOptions,
                scheduleInfo = presentationFields.scheduleInfo,
                recurrentScheduleInfo = presentationFields.recurrentScheduleInfo,
                mainActionNavigation = presentationFields.mainActionNavigation,
                secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                taskAlert = presentationFields.alert,
                copayInfo = presentationFields.copayInfo,
                expireDetails = presentationFields.expireDetails,
                drawers = presentationFields.drawers,
                canArchive = presentationFields.canArchive,
                archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                statusTag = presentationFields.statusTag,
                warning = presentationFields.warning,
                appointmentScheduleId = presentationFields.appointmentScheduleId,
                canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
            )
        }

    private fun convertInQuestionnaireResponse(
        task: QuestionnaireNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean,
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(task, isRedesignHealthPlanDetailsEnabled)
            .let { presentationFields ->
                ActionPlanTaskItemDetailResponse(
                    section = task.type,
                    healthPlanTaskId = task.id,
                    canAskHealthTeam = presentationFields.canAskHealthTeam,
                    description = task.title ?: "Questionário de Saúde",
                    staffInfo = getStaffInfo(task, staffs),
                    content = task.description,
                    createdAt = task.createdAt,
                    status = defineTaskStatus(task),
                    favorite = task.favorite,
                    finishTask = presentationFields.finishTask,
                    deleteTask = presentationFields.deleteTask,
                    mainActionNavigation = presentationFields.mainActionNavigation,
                    secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                    tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                    expireDetails = presentationFields.expireDetails,
                    drawers = presentationFields.drawers,
                    canArchive = presentationFields.canArchive,
                    archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                    statusTag = presentationFields.statusTag,
                    warning = presentationFields.warning,
                    appointmentScheduleId = presentationFields.appointmentScheduleId,
                    canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
                )
            }

    private suspend fun convertInFollowUpRequestResponse(
        task: FollowUpRequestNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(
            task,
            isRedesignHealthPlanDetailsEnabled,
        ).let { presentationFields ->
            ActionPlanTaskItemDetailResponse(
                section = task.type,
                healthPlanTaskId = task.id,
                canAskHealthTeam = presentationFields.canAskHealthTeam,
                description = task.title ?: "Consulta de Retorno",
                content = task.description,
                createdAt = task.createdAt,
                status = defineTaskStatus(task),
                staffInfo = getStaffInfo(task, staffs),
                favorite = task.favorite,
                navigations = presentationFields.navigations,
                finishTask = presentationFields.finishTask,
                hideScheduleOptions = presentationFields.hideScheduleOptions,
                scheduleInfo = presentationFields.scheduleInfo,
                recurrentScheduleInfo = presentationFields.recurrentScheduleInfo,
                mainActionNavigation = presentationFields.mainActionNavigation,
                secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                taskAlert = presentationFields.alert,
                copayInfo = presentationFields.copayInfo,
                expireDetails = presentationFields.expireDetails,
                drawers = presentationFields.drawers,
                canArchive = presentationFields.canArchive,
                archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                statusTag = presentationFields.statusTag,
                warning = presentationFields.warning,
                appointmentScheduleId = presentationFields.appointmentScheduleId,
                canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
            )
        }

    private suspend fun convertInSurgeryPrescriptionResponse(
        task: SurgeryPrescriptionNew,
        staffs: List<Staff>,
        isRedesignHealthPlanDetailsEnabled: Boolean
    ): ActionPlanTaskItemDetailResponse =
        actionPlanTaskPresentationBuilder.getPresentationFields(
            task,
            isRedesignHealthPlanDetailsEnabled,
        ).let { presentationFields ->
            ActionPlanTaskItemDetailResponse(
                section = task.type,
                healthPlanTaskId = task.id,
                canAskHealthTeam = presentationFields.canAskHealthTeam,
                description = task.title ?: "Tarefa de prescrição cirúrgica˜",
                content = task.description,
                createdAt = task.createdAt,
                status = defineTaskStatus(task),
                attachments = presentationFields.attachments,
                staffInfo = getStaffInfo(task, staffs),
                favorite = task.favorite,
                navigations = presentationFields.navigations,
                finishTask = presentationFields.finishTask,
                deleteTask = presentationFields.deleteTask,
                hideScheduleOptions = presentationFields.hideScheduleOptions,
                scheduleInfo = presentationFields.scheduleInfo,
                mainActionNavigation = presentationFields.mainActionNavigation,
                secondaryActionNavigation = presentationFields.secondaryActionNavigation,
                tertiaryActionNavigation = presentationFields.tertiaryActionNavigation,
                expireDetails = presentationFields.expireDetails,
                drawers = presentationFields.drawers,
                canArchive = presentationFields.canArchive,
                archiveConfirmationModal = presentationFields.archiveConfirmationModal,
                statusTag = presentationFields.statusTag,
                warning = presentationFields.warning,
                appointmentScheduleId = presentationFields.appointmentScheduleId,
                canEditAppointmentSchedule = presentationFields.canEditAppointmentSchedule,
            )
        }

    private fun defineTaskStatus(task: ActionPlanTask) =
        when (task.status) {
            ActionPlanTaskStatus.ACTIVE,
            ActionPlanTaskStatus.ACTIVE_ON_GOING -> ActionPlanItemResponseStatus.TO_DO

            ActionPlanTaskStatus.SCHEDULED -> ActionPlanItemResponseStatus.SCHEDULED
            ActionPlanTaskStatus.OVERDUE -> ActionPlanItemResponseStatus.OVERDUE
            ActionPlanTaskStatus.EXPIRED -> ActionPlanItemResponseStatus.EXPIRED
            ActionPlanTaskStatus.DONE -> ActionPlanItemResponseStatus.DONE
            ActionPlanTaskStatus.DELETED_BY_MEMBER,
            ActionPlanTaskStatus.DELETED,
            ActionPlanTaskStatus.DELETED_BY_STAFF -> ActionPlanItemResponseStatus.DELETED_BY_MEMBER
        }

    private fun createTaskRequester(
        staffId: UUID,
        staff: Staff,
        requestedAt: LocalDateTime? = null,
    ) = TaskRequesterRequest(
        staffId = staffId,
        name = staff.fullName,
        profileImageUrl = staff.profileImageUrl,
        requestedAt = requestedAt.toString(),
        role = roleOfTaskStaff(staff),
    )

    private fun roleOfTaskStaff(staff: Staff) =
        staff.role.description

    private fun getStaffInfo(
        task: ActionPlanTask,
        staffs: List<Staff>,
    ) =
        task.releasedByStaffId?.let { staffId ->
            createTaskRequester(
                staffId,
                staffs.first { it.id == task.releasedByStaffId },
                task.releasedAt
            )
        }

    private fun buildFrequency(task: ActionPlanTask) =
        task.frequency?.let { FrequencyTransportConverter.convert(it) }

    private suspend fun getPreparations(taskDetails: ActionPlanTaskDetails) =
        taskDetails.task
            .map { it.specialize<TestRequestNew>() }
            .mapNotNull { it.code }
            .let { testCodesService.getPreparations(it) }
            .map {
                TestPreparationTransport(
                    it.value.title,
                    it.value.instructions,
                )
            }

    private fun isRedesignHealthPlanDetailsEnabled(appVersion: SemanticVersion) = FeatureService.get(
        namespace = FeatureNamespace.ALICE_APP,
        key = "redesigned_health_plan_details_min_version",
        defaultValue = "99.99.99"
    ).let { SemanticVersion(it) <= appVersion }

}
