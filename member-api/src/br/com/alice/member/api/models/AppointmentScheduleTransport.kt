package br.com.alice.member.api.models

import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.AppointmentScheduleEventTypeLocation
import br.com.alice.data.layer.models.AppointmentScheduleType
import java.util.UUID

data class NextAppointmentSchedules(
    val appointmentSchedules: List<AppointmentScheduleResponse>
)
data class AppointmentScheduleResponse(
    val id: String,
    val healthProfessionals: List<HealthProfessionalResponse> = emptyList(),
    val name: String,
    val startTime: String,
    val endTime: String? = null,
    val cancelUrl: String? = "",
    val rescheduleUrl: String? = "",
    val location: String,
    val locationUrl: String? = null,
    val checkInStatus: AppointmentScheduleCheckInStatus? = null
)

data class AppointmentScheduleEventTypeResponse(
    val duration: Int,
    val title: String,
    val isMultiProfessionalReferral: Boolean,
    val numberOfDaysFromNowToAllowScheduling: Int,
)

data class AppointmentScheduleEventTypesSetupResponse(
    val events: List<AppointmentScheduleEventTypeSetupResponse>? = null,
    val staff: StaffResponse? = null
)

data class AppointmentScheduleEventTypeSetupResponse(
    val id: UUID,
    val duration: Int,
    val title: String,
    val isMultiProfessionalReferral: Boolean,
    val numberOfDaysFromNowToAllowScheduling: Int,
    val providerType: AppointmentScheduleEventTypeLocation,
    val isSelected: Boolean,
    val category: AppointmentScheduleType,
)

data class ProviderUnitSetupResponse(
    val id: UUID,
    val name: String,
    val numberOfDaysFromNowToAllowScheduling: Int,
    val duration: Int,
)

data class EventProviderUnitResponse(
    val providerUnits: List<ProviderUnitSetupResponse>
)

data class StaffsRelatedToActiveConditionsResponse(
    val staffs: List<StaffResponse>,
)

data class HasCollidingSchedulesResponse(
    val hasCollidingSchedules: Boolean
)

data class FirstAppointmentScheduleResponse(
    val appointmentScheduleEventTypeId: UUID? = null
)
