package br.com.alice.member.api.models.v2

import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.EmergencyNew
import br.com.alice.data.layer.models.FollowUpRequestNew
import br.com.alice.data.layer.models.GenericTaskNew
import br.com.alice.data.layer.models.PrescriptionNew
import br.com.alice.data.layer.models.QuestionnaireNew
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.TestRequestNew
import br.com.alice.healthplan.models.TaskRequesterRequest
import br.com.alice.member.api.builders.DeepLinkBuilder
import br.com.alice.member.api.extensions.toActionNavigationConfirmationModal
import br.com.alice.member.api.models.ActionPlanItemResponseStatus
import br.com.alice.member.api.models.DeadlineTransport
import br.com.alice.member.api.models.FrequencyTransport
import br.com.alice.member.api.models.HealthPlanActionButton
import br.com.alice.member.api.models.HealthPlanItemAttachment
import br.com.alice.member.api.models.HealthPlanItemDrawer
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemStatusTag
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PreparationTransport
import br.com.alice.member.api.models.ScheduleInfo
import br.com.alice.member.api.models.StartTransport
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import java.time.LocalDateTime
import java.util.UUID

data class ActionPlanTaskItemDetailResponse(
    val section: ActionPlanTaskType,
    val healthPlanTaskId: UUID? = null,
    val description: String = "",
    val content: String? = null,
    val status: ActionPlanItemResponseStatus? = null,
    @Deprecated("Deprecated on redesign. Use ExpireDetails instead")
    val createdAt: LocalDateTime? = null,
    @Deprecated("Deprecated on redesign. Use ExpireDetails instead")
    val frequency: FrequencyTransport? = null,
    @Deprecated("Deprecated on redesign. Use ExpireDetails instead")
    val deadline: DeadlineTransport? = null,
    @Deprecated("Deprecated on redesign. Use ExpireDetails instead")
    val start: StartTransport? = null,
    val attachments: List<HealthPlanItemAttachment>? = emptyList(),
    val infos: List<String> = emptyList(),
    val navigations: List<HealthPlanItemNavigation>? = emptyList(),
    val staffInfo: TaskRequesterRequest? = null,
    @Deprecated("Deprecated on redesign.")
    val favorite: Boolean? = false,
    // Prescription
    val action: String? = null,
    // TestRequest
    @Deprecated("Use HealthPlanPresentationFields instead")
    val preparation: PreparationTransport? = null,
    val relatedTaskIds: List<String>? = null,
    // Referral
    val hideScheduleOptions: Boolean? = false,
    // TestRequest and Referral
    val scheduleInfo: ScheduleInfo? = null,
    val recurrentScheduleInfo: ScheduleInfo? = null,
    // making finish and delete button dynamic
    val finishTask: HealthPlanActionButton? = null,
    val deleteTask: HealthPlanActionButton? = null,
    val canAskHealthTeam: Boolean = false,
    val taskAlert: HealthPlanItemAlert? = null,
    val copayInfo: HealthPlanItemLink? = null,

    // redesign fields
    val expireDetails: String? = null,
    val drawers: List<HealthPlanItemDrawer>? = null,
    val canArchive: Boolean? = null,
    val archiveConfirmationModal: ActionNavigationConfirmationModal? = null,
    val mainActionNavigation: ActionNavigation? = null,
    val secondaryActionNavigation: ActionNavigation? = null,
    val tertiaryActionNavigation: ActionNavigation? = null,
    val statusTag: HealthPlanItemStatusTag? = null,
    val warning: String? = null,
    val appointmentScheduleId: String? = null,
    val canEditAppointmentSchedule: Boolean = true,
)

enum class ActionNavigationType {
    NAVIGATION,
    CONCLUSION
}

data class ActionNavigation(
    val type: ActionNavigationType = ActionNavigationType.NAVIGATION,
    val label: String,
    val navigation: NavigationResponse? = null,
    val confirmationModal: ActionNavigationConfirmationModal? = null
) {
    companion object {

        fun followUpAction(task: FollowUpRequestNew) =
            takeIf { task.isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = "Agendar",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
                        properties = mapOf(
                            "method" to "GET",
                            "endpoint" to "/accredited_network/healthcare_team/physician/${task.releasedByStaffId}",
                            "selected_tab" to "1",
                        )
                    )
                ).takeIf { task.status != ActionPlanTaskStatus.SCHEDULED }
            }

        fun questionnaireAction(task: QuestionnaireNew) = ActionNavigation(
            label = "Responder",
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.EXTERNAL_APP,
                properties = mapOf(
                    "link" to DeepLinkBuilder.buildLinkForQuestionnaireFromTask(task)
                )
            )
        )

        fun emergencyAction(task: EmergencyNew) =
            takeIf { task.isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = "Ver rede",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_SCHEDULE,
                        properties = mapOf(
                            "task_id" to task.id
                        )
                    )
                )
            }

        fun emergencyIsDoneAction(task: EmergencyNew) =
            takeIf { task.isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = "Concluído",
                    type = ActionNavigationType.CONCLUSION,
                )
            }

        fun genericIsDoneAction(task: GenericTaskNew) =
            takeIf { task.isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = "Orientação concluída",
                    confirmationModal = ActionNavigationConfirmationModal(
                        title = "Orientação concluída",
                        description = "Essa orientação será guardada no menu superior do seu Plano de Ação, você poderá ver ela novamente se precisar",
                        confirmButtonLabel = "Concluir tarefa",
                        cancelButtonLabel = "Cancelar"
                    ),
                    type = ActionNavigationType.CONCLUSION,
                )
            }

        fun testRequestAction(
            authorization: ActionPlanTaskAuthorization?,
            task: TestRequestNew,
        ) =
            takeIf { task.isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = "Ver laboratórios",
                    confirmationModal = authorization?.toActionNavigationConfirmationModal(),
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_SCHEDULE,
                        properties = mapOf(
                            "task_id" to task.id
                        )
                    )
                )
            }

        fun testRequestMultipleAction(tasks: List<TestRequestNew>) =
            takeIf { tasks.first().isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = "Ver laboratórios",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_SCHEDULE,
                        properties = mapOf(
                            "task_id" to tasks.first().id
                        )
                    )
                )
            }

        fun testRequestIsDoneAction(task: TestRequestNew) =
            takeIf { task.isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = "Já fiz o exame",
                    type = ActionNavigationType.CONCLUSION,
                )
            }

        fun testRequestIsDoneMultipleAction(tasks: List<TestRequestNew>) =
            takeIf { tasks.first().isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = "Já fiz o exame",
                    type = ActionNavigationType.CONCLUSION,
                )
            }

        fun referralScheduleAction(task: ReferralNew, isTherapy: Boolean) =
            takeIf { task.isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = if (isTherapy) "Ver rede" else "Agendar",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_SCHEDULE,
                        properties = mapOf(
                            "task_id" to task.id
                        )
                    )
                )
            }


        fun referralAlreadyScheduledAction(task: ReferralNew) =
            takeIf { task.isAllowedSchedule() }?.let {
                ActionNavigation(
                    label = if (task.status == ActionPlanTaskStatus.SCHEDULED) "Alterei a consulta" else "Já agendei a consulta",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.HEALTH_PLAN_EXTERNAL_SCHEDULE,
                        properties = mapOfNotNull(
                            "health_plan_task_id" to task.id,
                            takeIf { task.status == ActionPlanTaskStatus.SCHEDULED }?.let {
                                "scheduled_at" to task.scheduledAt!!
                            },
                            "pop_until_route_name" to "health_plan_details"
                        )

                    )
                )
            }

        fun referralScheduleIsDoneAction(task: ReferralNew, isTherapy: Boolean) =
            takeIf { task.isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = if (isTherapy) "Terminei o tratamento" else "Já fiz a consulta",
                    type = ActionNavigationType.CONCLUSION,
                    confirmationModal = takeIf { isTherapy }?.let { ActionNavigationConfirmationModal.therapyIsDoneConfirmation() }
                )
            }

        fun prescriptionIsDoneAction(task: PrescriptionNew) =
            takeIf { task.isAllowedConclusion() }?.let {
                ActionNavigation(
                    label = "Terminei de tomar",
                    type = ActionNavigationType.CONCLUSION,
                )
            }


    }
}

data class ActionNavigationConfirmationModal(
    val title: String,
    val imageUrl: String? = null,
    val description: String? = null,
    val confirmButtonLabel: String? = null,
    val cancelButtonLabel: String? = null,
) {
    companion object {
        fun archiveConfirmation() = ActionNavigationConfirmationModal(
            title = "Arquivar tarefa",
            description = "Você ainda poderá acessar essa tarefa pelo menu superior no seu Plano de Ação",
            confirmButtonLabel = "Sim, arquivar",
            cancelButtonLabel = "Cancelar",
        )


        fun therapyIsDoneConfirmation() = ActionNavigationConfirmationModal(
            title = "Tratamento concluído",
            description = "Ao concluir o tratamento você não poderá mais agendar as próximas sessões para essa encaminhamento. Deseja concluir o tratamento?",
            confirmButtonLabel = "Concluir",
            cancelButtonLabel = "Cancelar",
        )

    }
}

data class HealthPlanItemAlert(
    val id: String,
    val title: String,
    val description: String,
    val illustration: String? = null,
    val navigation: NavigationResponse? = null,
)

data class HealthPlanItemLink(
    val icon: String,
    val label: String,
    val navigation: NavigationResponse? = null,
)
