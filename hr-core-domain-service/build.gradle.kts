plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.hr-core-domain-service"
version = aliceHrCoreDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "$buildDir/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:hr-core-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":common-kafka"))
    implementation(project(":communication"))


    implementation(project(":data-packages:hr-core-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:health-logic-domain-service-data-package"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:money-in-domain-service-data-package"))

    implementation(project(":data-layer-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":hr-core-domain-client"))
    implementation(project(":business-domain-client"))
    implementation(project(":health-logic-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":money-in-domain-client"))
    implementation(project(":product-domain-client"))

    ktor2Dependencies()
    implementation("com.amazonaws:aws-java-sdk-s3:$awsVersion")
    implementation("software.amazon.awssdk:pinpoint:$awsSdkVersion")
    implementation("org.apache.poi:poi:5.2.3")
    implementation("org.apache.poi:poi-ooxml:5.2.3")

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
}
