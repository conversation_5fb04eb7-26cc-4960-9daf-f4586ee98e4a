package br.com.alice.hr.core.services.useCases

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.clients.AmazonS3ClientImpl
import br.com.alice.hr.core.services.useCases.GenerateTemplateSheetsUseCase
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Test
import java.io.ByteArrayInputStream
import java.io.File
import kotlin.test.assertEquals

class GenerateTemplateSheetsUseCaseTest {
    private val getBeneficiaryRelationTypeOptionsUseCase = mockk<GetBeneficiaryRelationTypeOptionsUseCase>()
    private val generateTemplateSheetsUseCase = GenerateTemplateSheetsUseCase(
        getBeneficiaryRelationTypeOptionsUseCase
    )
    private val companySubContractList = listOf(TestModelFactory.buildCompanySubContract(), TestModelFactory.buildCompanySubContract(), TestModelFactory.buildCompanySubContract())
    private val company = TestModelFactory.buildCompany()
    private val companyProductPriceListingList = listOf(TestModelFactory.buildCompanyProductPriceListing(), TestModelFactory.buildCompanyProductPriceListing(), TestModelFactory.buildCompanyProductPriceListing())
    private val productList = listOf(
        TestModelFactory.buildProduct(displayName = "Teste"),
        TestModelFactory.buildProduct(displayName = "Teste 2"),
        TestModelFactory.buildProduct(displayName = "Teste 3")
    )
    private val amazonS3Client = AmazonS3ClientImpl()
    private val dependentRelationTypeList = listOf(
        ParentBeneficiaryRelationType.PARTNER,
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.CHILD,
        ParentBeneficiaryRelationType.SPOUSE
    )

    @Test
    fun `#run should return array of bytes from modified template sheet`() = runBlocking {
        val fileInputStream = File(javaClass.classLoader.getResource("InsertMemberTemplateSheet.xlsx")!!.path).inputStream()
        val arrayByteResult = File(javaClass.classLoader.getResource("InsertMemberTemplateSheetResult.xlsx")!!.path).readBytes()
        val dataValidationResult = getDataValidationFromSheet(arrayByteResult)

        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(company, any()) } returns dependentRelationTypeList.success()

        val result = generateTemplateSheetsUseCase.run(fileInputStream, company, companySubContractList, productList).get()

        assertEquals(getDataValidationFromSheet(result), dataValidationResult)

        coVerify { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    @Test
    fun `#run should return array of bytes when getBeneficiaryRelationTypeOptionsUseCase returns dependent relation type list default`() = runBlocking {
        val fileInputStream = File(javaClass.classLoader.getResource("InsertMemberTemplateSheet.xlsx")!!.path).inputStream()
        val arrayByteResult = File(javaClass.classLoader.getResource("InsertMemberTemplateSheetDefaultResult.xlsx")!!.path).readBytes()
        val dataValidationResult = getDataValidationFromSheet(arrayByteResult)

        coEvery { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) } returns Exception("Error").failure()

        val result = generateTemplateSheetsUseCase.run(fileInputStream, company, companySubContractList, productList).get()

        assertEquals(getDataValidationFromSheet(result), dataValidationResult)

        coVerify { getBeneficiaryRelationTypeOptionsUseCase.run(any(), any()) }
    }

    private fun getDataValidationFromSheet(data: ByteArray): List<String> {
        val workbook = XSSFWorkbook(ByteArrayInputStream(data))
        val sheet = workbook.getSheetAt(0)

        return sheet.getDataValidations().map { it.prettyPrint() }
    }
}