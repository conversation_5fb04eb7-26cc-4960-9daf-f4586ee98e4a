package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.model.DependentTransport
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID

class CreateBeneficiaryDependentUseCaseTest {
    private val beneficiaryService = mockk<BeneficiaryService>()
    private val createBeneficiaryDependentUseCase = CreateBeneficiaryDependentUseCase(beneficiaryService)

    private val company = TestModelFactory.buildCompany()
    private val subcontract = TestModelFactory.buildCompanySubContract(companyId = company.id)
    private val relationTypes = listOf(ParentBeneficiaryRelationType.CHILD)
    private val beneficiary = TestModelFactory.buildBeneficiary(companyId = company.id)

    private val address = Address(
        state = State.SP,
        city = "São Paulo",
        street = "123 Main St",
        number = "123"
    )

    private val dependentTransport = DependentTransport(
        firstName = "John",
        lastName = "Doe",
        mothersName = "Jane Doe",
        nationalId = "1234567890",
        email = "<EMAIL>",
        sex = Sex.MALE,
        birthDate = LocalDateTime.now(),
        phoneNumber = "1234567890",
        activatedAt = LocalDateTime.now(),
        address = address,
        productId = UUID.randomUUID(),
        parentBeneficiary = UUID.randomUUID(),
        parentBeneficiaryRelationType = ParentBeneficiaryRelationType.CHILD,
        parentBeneficiaryRelatedAt = LocalDateTime.now(),
        cnpj = company.cnpj,
        subcontractId = subcontract.id
    )

    private val beneficiaryTransport = BeneficiaryTransport(
        parentBeneficiary = dependentTransport.parentBeneficiary,
        parentBeneficiaryRelationType = dependentTransport.parentBeneficiaryRelationType,
        parentBeneficiaryRelatedAt = dependentTransport.parentBeneficiaryRelatedAt,
        companyId = company.id,
        type = BeneficiaryType.DEPENDENT,
        activatedAt = dependentTransport.activatedAt,
        cnpj = dependentTransport.cnpj.nullIfBlank(),
        birthDate = dependentTransport.birthDate,
        address = dependentTransport.address,
        email = dependentTransport.email,
        firstName = dependentTransport.firstName,
        lastName = dependentTransport.lastName,
        mothersName = dependentTransport.mothersName,
        nationalId = dependentTransport.nationalId,
        phoneNumber = dependentTransport.phoneNumber,
        sex = dependentTransport.sex,
        initialProductId = dependentTransport.productId,
        companySubContractId = dependentTransport.subcontractId,
    )

    @Test
    fun `#run should create a beneficiary dependent`() = runBlocking {
        coEvery { beneficiaryService.createBeneficiary(beneficiaryTransport, dependentTransport.productId, BeneficiaryOnboardingFlowType.UNDEFINED, null) } returns beneficiary.success()

        val result = createBeneficiaryDependentUseCase.run(dependentTransport, company, relationTypes)

        assertThat(result).isSuccessWithData(beneficiary)

        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }

    @Test
    fun `#run should return BadRequest when relation type is not valid`() = runBlocking {
        val result = createBeneficiaryDependentUseCase.run(dependentTransport, company, listOf(ParentBeneficiaryRelationType.SPOUSE))

        assertThat(result).isFailureOfType(BadRequestException::class)
    }

    @Test
    fun `#run should return Exception when beneficiaryService returns an Exception`() = runBlocking {
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns Exception("Error").failure()

        val result = createBeneficiaryDependentUseCase.run(dependentTransport, company, relationTypes)

        assertThat(result).isFailureOfType(Exception::class)
        coVerifyOnce { beneficiaryService.createBeneficiary(any(), any(), any(), any()) }
    }
}
