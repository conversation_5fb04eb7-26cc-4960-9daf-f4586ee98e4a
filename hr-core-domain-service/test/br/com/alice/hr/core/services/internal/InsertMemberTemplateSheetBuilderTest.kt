package br.com.alice.hr.core.services.internal

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.junit.jupiter.api.Test
import java.io.File
import kotlinx.coroutines.runBlocking
import kotlin.test.assertEquals
import java.io.ByteArrayInputStream

class InsertMemberTemplateSheetBuilderTest {

    val fileInputStream = File(javaClass.classLoader.getResource("InsertMemberTemplateSheet.xlsx")!!.path).inputStream()
    val arrayByteResult = File(javaClass.classLoader.getResource("InsertMemberTemplateSheetResult.xlsx")!!.path).readBytes()
    private val productList = listOf(
        TestModelFactory.buildProduct(displayName = "Teste"),
        TestModelFactory.buildProduct(displayName = "Teste 2"),
        TestModelFactory.buildProduct(displayName = "Teste 3")
    ).map { it.displayName ?: "" }

    private val dependentRelationTypeList = listOf(
        ParentBeneficiaryRelationType.SPOUSE.description,
        ParentBeneficiaryRelationType.CHILD.description,
        ParentBeneficiaryRelationType.PARTNER.description
    )

    @Test
    fun `#buildSheet should return success when build sheet`() = runBlocking {
        val result = InsertMemberTemplateSheetBuilder(fileInputStream, productList, dependentRelationTypeList).buildSheet()

        assertEquals(getDataValidationFromSheet(result.get()), getDataValidationFromSheet(arrayByteResult))
    }

    private fun getDataValidationFromSheet(data: ByteArray): List<String> {
        val workbook = XSSFWorkbook(ByteArrayInputStream(data))
        val sheet = workbook.getSheetAt(0)

        return sheet.getDataValidations().map { it.prettyPrint() }
    }
}