package br.com.alice.hr.core.services.useCases

import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.services.internal.InsertMemberTemplateSheetBuilder
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError

import java.io.InputStream

class GenerateTemplateSheetsUseCase(
    private val getBeneficiaryRelationTypeOptionsUseCase: GetBeneficiaryRelationTypeOptionsUseCase
) : Spannable {

    suspend fun run(templateSheetContent: InputStream, company: Company, subContracts: List<CompanySubContract>, productList: List<Product>): Result<ByteArray, Throwable> = span("generateTemplateSheets") {
        val productTitles = fetchProductTitleList(productList)

        fetchDependentRelationTypeList(company, subContracts).flatMap { dependentRelationTypeList ->
            InsertMemberTemplateSheetBuilder(templateSheetContent, productTitles, dependentRelationTypeList).buildSheet()
        }
    }

    private suspend fun fetchProductTitleList(productList: List<Product>): List<String> = span("fetchProductTitleList") {
        productList.map { it.displayName ?: null }.filterNotNull().distinct().sorted()
    }

    private suspend fun fetchDependentRelationTypeList(company: Company, subContracts: List<CompanySubContract>): Result<List<String>, Throwable> = span("fetchDependentRelationTypeList") {
        subContracts.map { subContract ->
            getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract).fold(
                success = {
                    it
                },
                failure = {
                    logger.error("Error fetching dependent relation type list", it)

                    emptyList()
                }
            )
        }
        .flatten()
        .listOrDefault()
        .map { it.description }
        .distinct()
        .sorted()
        .success()
    }

    private suspend fun List<ParentBeneficiaryRelationType>.listOrDefault(): List<ParentBeneficiaryRelationType> =
        if (isEmpty()) {
            ParentBeneficiaryRelationType.values().toList()
        } else {
            this
        }
}