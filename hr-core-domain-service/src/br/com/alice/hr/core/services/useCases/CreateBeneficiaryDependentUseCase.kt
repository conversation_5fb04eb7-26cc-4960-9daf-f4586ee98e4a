package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.model.DependentTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import java.util.UUID

class CreateBeneficiaryDependentUseCase(private val beneficiaryService: BeneficiaryService) : Spannable {
    suspend fun run(dependent: DependentTransport, company: Company, relationTypes: List<ParentBeneficiaryRelationType>): Result<Beneficiary, Throwable> = span("createBeneficiaryDependentUseCase") {
        if (isRelationTypeValid(dependent, relationTypes)) {
            createBeneficiaryDependent(dependent, company)
        } else {
            logger.error("Parent beneficiary relation type not allowed for this subcontract", 
                "parent_beneficiary_relation_type" to dependent.parentBeneficiaryRelationType,
                "parent_beneficiary_id" to dependent.parentBeneficiary,
                "company_id" to company.id
            )

            BadRequestException("Parent beneficiary relation type not allowed for this subcontract").failure()
        }
    }

    private fun isRelationTypeValid(dependent: DependentTransport, relationTypes: List<ParentBeneficiaryRelationType>): Boolean {
        return relationTypes.contains(dependent.parentBeneficiaryRelationType)
    }

    private suspend fun createBeneficiaryDependent(dependent: DependentTransport, company: Company): Result<Beneficiary, Throwable> {
        val flowType = if (company.companySize == CompanySize.SMALL) BeneficiaryOnboardingFlowType.FULL_RISK_FLOW else BeneficiaryOnboardingFlowType.UNDEFINED
        val beneficiaryTransport = dependent.toBeneficiaryTransport(company.id)
        return beneficiaryService.createBeneficiary(beneficiaryTransport, dependent.productId, flowType, null)
    }

    private fun DependentTransport.toBeneficiaryTransport(companyId: UUID): BeneficiaryTransport =
        BeneficiaryTransport(
            parentBeneficiary = this.parentBeneficiary,
            parentBeneficiaryRelationType = this.parentBeneficiaryRelationType,
            parentBeneficiaryRelatedAt = this.parentBeneficiaryRelatedAt,
            companyId = companyId,
            type = BeneficiaryType.DEPENDENT,
            activatedAt = this.activatedAt,
            cnpj = this.cnpj.nullIfBlank(),
            birthDate = this.birthDate,
            address = this.address,
            email = this.email,
            firstName = this.firstName,
            lastName = this.lastName,
            mothersName = this.mothersName,
            nationalId = this.nationalId,
            phoneNumber = this.phoneNumber,
            sex = this.sex,
            initialProductId = this.productId,
            companySubContractId = this.subcontractId
        )
}