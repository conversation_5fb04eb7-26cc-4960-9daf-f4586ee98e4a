package br.com.alice.hr.core.services.internal

import br.com.alice.common.observability.Spannable
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import org.apache.poi.ss.util.CellRangeAddressList
import org.apache.poi.xssf.usermodel.XSSFDataValidation
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.ByteArrayOutputStream
import java.io.InputStream

class InsertMemberTemplateSheetBuilder(
    private val templateSheetContent: InputStream,
    private val productTitles: List<String>,
    private val dependentRelationTypeList: List<String>
) : Spannable {
    companion object {
        const val FIRST_ROW_CONTENT_INDEX = 2
        const val HEADER_CELL_INDEX = 1
        const val PRODUCT_LIST_COLUMN_INDEX = 11
        const val RELATION_TYPE_COLUMN_INDEX = 18
        const val PRODUCT_COLUMN_INDEX_STRING = "L3"
        const val RELATION_TYPE_COLUMN_INDEX_STRING = "S3"
        const val FIRST_SHEET_INDEX = 0
    }

    suspend fun buildSheet(): Result<ByteArray, Throwable> = span("buildSheet") {
        try {
            val workbook = XSSFWorkbook(templateSheetContent)
            val sheet = workbook.getSheetAt(FIRST_SHEET_INDEX)

            changeDropDownListValues(sheet, productTitles, dependentRelationTypeList)

            val byteArrayResult = workbook.toByteArrayResult()
            workbook.close()
            byteArrayResult.success()
        } catch (e: Exception) {
            e.failure()
        }
    }

    private suspend fun changeDropDownListValues(sheet: XSSFSheet, productTitles: List<String>, dependentRelationTypeList: List<String>) {
        var firstRowIndex = sheet.getFirstRowNum() + FIRST_ROW_CONTENT_INDEX
        var lastRowIndex = sheet.getLastRowNum()

        sheet.removeDataValidationsToBeEdited()

        insertNewDataValidation(sheet, firstRowIndex, lastRowIndex, PRODUCT_LIST_COLUMN_INDEX, productTitles)
        insertNewDataValidation(sheet, firstRowIndex, lastRowIndex, RELATION_TYPE_COLUMN_INDEX, dependentRelationTypeList)
    }

    private suspend fun insertNewDataValidation(sheet: XSSFSheet, firstRowIndex: Int, lastRowIndex: Int, columnIndex: Int, constraint: List<String>) {
        val dataValidationsHelper = XSSFDataValidationHelper(sheet)
        val addressList = CellRangeAddressList(firstRowIndex, lastRowIndex, columnIndex, columnIndex)
        val dvConstraint = dataValidationsHelper.createExplicitListConstraint(constraint.toTypedArray())
        val dataValidation = dataValidationsHelper.createValidation(dvConstraint, addressList)
        sheet.addValidationData(dataValidation)
    }

    private fun XSSFSheet.removeDataValidationsToBeEdited() {
        val filteredDataValidations = this.getDataValidationsToKeep()
        this.clearAllDataValidations()
        val dataValidationsHelper = XSSFDataValidationHelper(this)
        for (dv in filteredDataValidations) {
            val dataValidation = dataValidationsHelper.createValidation(dv.getValidationConstraint(), dv.getRegions())
            this.addValidationData(dataValidation)
        }
    }

    private fun XSSFSheet.clearAllDataValidations() {
        val dataValidations = this.getCTWorksheet().getDataValidations()
        dataValidations.dataValidationList.clear()
    }

    private fun XSSFSheet.getDataValidationsToKeep(): List<XSSFDataValidation> = this.getDataValidations().filter { filterDataValidationsLogic(it.prettyPrint()) }

    private fun filterDataValidationsLogic(addressListString: String): Boolean =
        !(PRODUCT_COLUMN_INDEX_STRING in addressListString)
        && !(RELATION_TYPE_COLUMN_INDEX_STRING in addressListString)

    private fun XSSFWorkbook.toByteArrayResult(): ByteArray {
        val tempOutputStream = ByteArrayOutputStream()
        this.write(tempOutputStream)
        return tempOutputStream.toByteArray()
    }
}
