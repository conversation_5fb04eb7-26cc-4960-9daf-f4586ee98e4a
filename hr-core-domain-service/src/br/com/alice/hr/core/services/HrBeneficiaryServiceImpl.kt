package br.com.alice.hr.core.services

import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.AccessForbiddenException
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.hr.core.ServiceConfig
import br.com.alice.hr.core.client.HrBeneficiaryService
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.DependentTransport
import br.com.alice.hr.core.model.TemplateSheetResult
import br.com.alice.hr.core.clients.interfaces.AmazonS3Client
import br.com.alice.hr.core.services.useCases.CreateBeneficiaryDependentUseCase
import br.com.alice.hr.core.services.useCases.GenerateTemplateSheetsUseCase
import br.com.alice.hr.core.services.useCases.GetBeneficiaryRelationTypeOptionsUseCase
import br.com.alice.hr.core.services.useCases.StaticValidateBeneficiariesUseCase
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.success
import java.util.UUID

class HrBeneficiaryServiceImpl(
    private val companyService: CompanyService,
    private val companySubContractService: CompanySubContractService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val productService: ProductService,
    private val getBeneficiaryRelationTypeOptionsUseCase: GetBeneficiaryRelationTypeOptionsUseCase,
    private val createBeneficiaryDependentUseCase: CreateBeneficiaryDependentUseCase,
    private val staticValidateBeneficiariesUseCase: StaticValidateBeneficiariesUseCase,
    private val generateTemplateSheetsUseCase: GenerateTemplateSheetsUseCase,
    private val amazonS3Client: AmazonS3Client
) : HrBeneficiaryService {

    companion object {
        val s3SheetFileName = "member_template.xlsx"
    }

    override suspend fun getRelationTypes(companyId: UUID, subcontractId: UUID): Result<List<ParentBeneficiaryRelationType>, Throwable> =
        companyService.get(companyId).flatMap { company ->
            companySubContractService.get(subcontractId).flatMap { subcontract ->
                getBeneficiaryRelationTypeOptionsUseCase.run(company, subcontract)
            }
        }

    override suspend fun addBeneficiariesBatch(
        companyId: UUID,
        beneficiaryBatch: BeneficiaryBatchTransport
    ): Result<BeneficiaryBatchValidation, Throwable> =
        staticValidateBeneficiariesUseCase
            .run(beneficiaryBatch)

    override suspend fun createDependent(dependent: DependentTransport, companyId: UUID): Result<Beneficiary, Throwable> = span("createDependent") {
        companyService.get(companyId).flatMap { company ->
            companySubContractService.get(dependent.subcontractId).flatMap { subContract ->
                getBeneficiaryRelationTypeOptionsUseCase.run(company, subContract).flatMap { relationTypes ->
                    createBeneficiaryDependentUseCase.run(dependent, company, relationTypes)
                }
            }
        }.flatMapError {
            logger.error(
                "Error creating dependent",
                "error" to it,
                "parent_beneficiary_id" to dependent.parentBeneficiary,
                "company_id" to companyId,
                "subcontract_id" to dependent.subcontractId,
                "product_id" to dependent.productId,
                "parent_beneficiary_relation_type" to dependent.parentBeneficiaryRelationType
            )

            it.failure()
        }
    }

    override suspend fun getBeneficiarySheetTemplate(companyId: UUID): Result<TemplateSheetResult, Throwable> = span("getBeneficiarySheetTemplate") {
        val s3SheetPath = ServiceConfig.S3.templateSheetBucketUrl

        companyService.get(companyId).flatMap { company ->
            companySubContractService.findByCompanyId(companyId).flatMap { subContracts ->
                companyProductPriceListingService.findCurrentByCompanyIds(listOf(company.id)).flatMap { companyProductPriceListingList ->

                    val productIds = companyProductPriceListingList.map { it.productId }.distinct()

                    productService.findByIds(productIds).flatMap { productList ->
                        amazonS3Client.getS3ObjectContent(s3SheetPath).flatMap {
                            it.use { contentInputStream ->
                                generateTemplateSheetsUseCase.run(contentInputStream, company, subContracts, productList).flatMap { result ->
                                    TemplateSheetResult(result, s3SheetFileName).success()
                                }
                            }
                        }
                    }
                }
            }
        }.flatMapError { error ->
            logger.error(
                "Error generating beneficiary sheet template",
                "error" to error,
                "company_id" to companyId
            )

            error.toMappedError("Error generating beneficiary sheet template").failure()
        }
    }

    private fun Throwable.toMappedError(message: String): Throwable = when (this) {
        is DuplicatedItemException,
        is AuthorizationException,
        is AccessForbiddenException,
        is NotFoundException -> this

        else -> InternalServiceErrorException(message)
    }
}
