package br.com.alice.healthcondition

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.data.layer.HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.CaseRecordDataService
import br.com.alice.data.layer.services.CaseRecordDataServiceClient
import br.com.alice.data.layer.services.HealthConditionAxisDataService
import br.com.alice.data.layer.services.HealthConditionAxisDataServiceClient
import br.com.alice.data.layer.services.HealthConditionDataService
import br.com.alice.data.layer.services.HealthConditionDataServiceClient
import br.com.alice.data.layer.services.HealthConditionRelatedDataService
import br.com.alice.data.layer.services.HealthConditionRelatedDataServiceClient
import br.com.alice.data.layer.services.HealthConditionTemplateDataService
import br.com.alice.data.layer.services.HealthConditionTemplateDataServiceClient
import br.com.alice.data.layer.services.PersonCaseDataService
import br.com.alice.data.layer.services.PersonCaseDataServiceClient
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.healthcondition.client.CaseRecordService
import br.com.alice.healthcondition.client.HealthAnswersToCaseRecordService
import br.com.alice.healthcondition.client.HealthConditionAxisService
import br.com.alice.healthcondition.client.HealthConditionRelatedService
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthcondition.client.HealthConditionTemplateService
import br.com.alice.healthcondition.client.PersonCaseService
import br.com.alice.healthcondition.consumers.AppointmentConsumer
import br.com.alice.healthcondition.consumers.AppointmentScheduleConsumer
import br.com.alice.healthcondition.consumers.BUDConsumer
import br.com.alice.healthcondition.consumers.BackfillConsumer
import br.com.alice.healthcondition.consumers.CaseRecordConsumer
import br.com.alice.healthcondition.consumers.ChannelConsumer
import br.com.alice.healthcondition.consumers.CounterReferralConsumer
import br.com.alice.healthcondition.consumers.HealthDeclarationFinishedConsumer
import br.com.alice.healthcondition.consumers.LowRiskMemberStratificationConsumer
import br.com.alice.healthcondition.consumers.OutcomeRecommendationActionEventConsumer
import br.com.alice.healthcondition.consumers.PersonHealthEventConsumer
import br.com.alice.healthcondition.consumers.TertiaryIntentionTouchPointConsumer
import br.com.alice.healthcondition.controllers.CaseRecordBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionBackfillController
import br.com.alice.healthcondition.controllers.HealthConditionRelatedController
import br.com.alice.healthcondition.controllers.PersonCaseBackfillController
import br.com.alice.healthcondition.routes.apiRoutes
import br.com.alice.healthcondition.routes.kafkaRoutes
import br.com.alice.healthcondition.services.CaseRecordServiceImpl
import br.com.alice.healthcondition.services.HealthAnswersToCaseRecordServiceImpl
import br.com.alice.healthcondition.services.HealthConditionAxisServiceImpl
import br.com.alice.healthcondition.services.HealthConditionRelatedServiceImpl
import br.com.alice.healthcondition.services.HealthConditionServiceImpl
import br.com.alice.healthcondition.services.HealthConditionTemplateServiceImpl
import br.com.alice.healthcondition.services.PersonCaseServiceImpl
import br.com.alice.healthcondition.services.internal.AppointmentCaseRecordService
import br.com.alice.healthcondition.services.internal.HealthDeclarationCaseService
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        MembershipClientModule,
        QuestionnaireDomainClientModule,
        HealthPlanDomainClientModule,
        StaffDomainClientModule,
        ClinicalAccountDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single<Invoker> { DataLayerClientConfiguration.build() }

            //Health Controller
            single { HealthController(SERVICE_NAME) }

            //Load services
            loadServiceServers("br.com.alice.healthcondition.services")

            // Data Services
            single<CaseRecordDataService> { CaseRecordDataServiceClient(get()) }
            single<HealthConditionDataService> { HealthConditionDataServiceClient(get()) }
            single<HealthConditionAxisDataService> { HealthConditionAxisDataServiceClient(get()) }
            single<HealthConditionRelatedDataService> { HealthConditionRelatedDataServiceClient(get()) }
            single<HealthConditionTemplateDataService> { HealthConditionTemplateDataServiceClient(get()) }
            single<PersonCaseDataService> { PersonCaseDataServiceClient(get()) }

            //Internal Services
            single { HealthDeclarationCaseService(get(), get()) }
            single { AppointmentCaseRecordService(get(), get()) }

            //Consumers
            single { HealthDeclarationFinishedConsumer(get()) }
            single { AppointmentScheduleConsumer(get()) }
            single { CaseRecordConsumer(get<PersonCaseService>() as PersonCaseServiceImpl) }
            single { LowRiskMemberStratificationConsumer(get(), get()) }
            single { PersonHealthEventConsumer(get()) }
            single { AppointmentConsumer(get()) }
            single { ChannelConsumer(get<PersonCaseService>() as PersonCaseServiceImpl, get()) }
            single { CounterReferralConsumer(get(), get(), get()) }
            single { OutcomeRecommendationActionEventConsumer(get()) }
            single { BUDConsumer(get(), get(), get()) }
            single { TertiaryIntentionTouchPointConsumer(get(), get()) }
            single { BackfillConsumer(get()) }

            // Exposed Services
            single<CaseRecordService> { CaseRecordServiceImpl(get(), get(), get(), get()) }
            single<HealthConditionService> { HealthConditionServiceImpl(get(), get(), get()) }
            single<HealthConditionTemplateService> { HealthConditionTemplateServiceImpl(get()) }
            single<HealthConditionAxisService> { HealthConditionAxisServiceImpl(get()) }
            single<HealthConditionRelatedService> { HealthConditionRelatedServiceImpl(get()) }
            single<HealthAnswersToCaseRecordService> { HealthAnswersToCaseRecordServiceImpl(get(), get()) }
            single<PersonCaseService> { PersonCaseServiceImpl(get(), get()) }

            // Controllers
            single { PersonCaseBackfillController(get<PersonCaseService>() as PersonCaseServiceImpl, get()) }
            single { CaseRecordBackfillController(get<PersonCaseService>() as PersonCaseServiceImpl, get(), get()) }
            single { HealthConditionRelatedController(get(), get()) }
            single { HealthConditionBackfillController(get(), get(), get()) }
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules,
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        routing {
            application.attributes.put(PolicyRootServiceKey, HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.EHR,
            FeatureNamespace.HEALTH_LOGICS
        )
    }
}
