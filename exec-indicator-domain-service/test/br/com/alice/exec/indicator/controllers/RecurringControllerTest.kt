package br.com.alice.exec.indicator.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingUpdateService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RecurringControllerTest : RecurrentControllerTestHelper() {

    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService = mockk()


    private val executionGroupId = RangeUUID.generate()
    private val controller = RecurringController(
        resourceBundleSpecialtyPricingUpdateService,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    private val resourceBundleSpecialtyPricingUpdate = TestModelFactory.buildResourceBundleSpecialtyPricingUpdate()

    @Test
    fun `#checkPendingResourceBundleSpecialtyPricingUpdate - return true if there is pending update`() = runBlocking {
        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns resourceBundleSpecialtyPricingUpdate.success()

        internalAuthentication {
            post("/recurring_subscribers/check_pending_resource_bundle_specialty_pricing_update") { response ->
                ResponseAssert.assertThat(response).isOKWithData(true)
            }
        }
    }

    @Test
    fun `#checkPendingResourceBundleSpecialtyPricingUpdate - return false if there is no pending update`() = runBlocking {
        coEvery {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate()
        } returns NotFoundException("").failure()

        internalAuthentication {
            post("/recurring_subscribers/check_pending_resource_bundle_specialty_pricing_update") { response ->
                ResponseAssert.assertThat(response).isOKWithData(false)
            }
        }
    }
}
