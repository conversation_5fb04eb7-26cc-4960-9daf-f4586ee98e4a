package br.com.alice.exec.indicator.controllers

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.EXEC_INDICATOR_RECURRING_SERVICE_NAME
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingUpdateService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class RecurringController(
    private val resourceBundleSpecialtyPricingUpdateService: ResourceBundleSpecialtyPricingUpdateService,
) : Controller() {

    private suspend fun withRecurringEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(EXEC_INDICATOR_RECURRING_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(EXEC_INDICATOR_RECURRING_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

    suspend fun checkPendingResourceBundleSpecialtyPricingUpdate(): Response {
        return withRecurringEnvironment {
            resourceBundleSpecialtyPricingUpdateService.getProcessingResourceBundleSpecialtyPricingUpdate().map {
                logger.info("RecurringController::checkIfThereArePendingResourceBundleSpecialtyPricingUpdates there is a pending update",
                    "resourceBundleSpecialtyPricingUpdateId" to it.id
                )
                true
            }.foldNotFound {
                false.success()
            }.foldResponse()
        }
    }
}
