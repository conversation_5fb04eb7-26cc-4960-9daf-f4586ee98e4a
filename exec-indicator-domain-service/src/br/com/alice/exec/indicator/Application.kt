package br.com.alice.exec.indicator

import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.rfc.Invoker
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.communication.crm.CrmIdentityProvider
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.communication.crm.braze.BrazeAnalyticsTracker
import br.com.alice.communication.crm.braze.client.users.BrazeUserTrackClient
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.PinPointEmailClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.data.layer.EXEC_INDICATOR_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.*
import br.com.alice.eita.nullvs.ioc.EitaNullvsIntegrationClientModule
import br.com.alice.eventinder.ioc.EventinderDomainClientModule
import br.com.alice.exec.indicator.client.*
import br.com.alice.exec.indicator.consumers.AppointmentProcedureExecutedGroupConsumer
import br.com.alice.exec.indicator.consumers.EligibilityConsumer
import br.com.alice.exec.indicator.consumers.ExecuteTotvsGuiaProceduresV2Consumer
import br.com.alice.exec.indicator.consumers.ExecutionGroupConsumer
import br.com.alice.exec.indicator.consumers.GuiaWithProceduresUpsertedConsumer
import br.com.alice.exec.indicator.consumers.HealthEventUncoordinatedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareBundleUpsertedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareResourceGroupAssociationUpdatedConsumer
import br.com.alice.exec.indicator.consumers.HealthcareResourceUpsertedConsumer
import br.com.alice.exec.indicator.consumers.MagicNumbersConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentChemotherapyUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentOpmeUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsAttachmentRadiotherapyUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsGuiaExtensionUpsertedConsumer
import br.com.alice.exec.indicator.consumers.NullvsGuiaUpsertedConsumer
import br.com.alice.exec.indicator.consumers.ProcessedHealthEventsConsumer
import br.com.alice.exec.indicator.consumers.ResourceBundleSpecialtyPricingUpdateCreatedConsumer
import br.com.alice.exec.indicator.consumers.TotvsGuiaUpsertedConsumer
import br.com.alice.exec.indicator.consumers.UnlinkHealthcareResourceFromHealthcareBundleConsumer
import br.com.alice.exec.indicator.controllers.BackFillController
import br.com.alice.exec.indicator.controllers.GlossAuthorizationInfoController
import br.com.alice.exec.indicator.controllers.GuiaWithProceduresBackfillController
import br.com.alice.exec.indicator.controllers.HealthSpecialistResourceBundleBackfillController
import br.com.alice.exec.indicator.controllers.MvAuthorizedProcedureExecutorController
import br.com.alice.exec.indicator.controllers.RecurringController
import br.com.alice.exec.indicator.controllers.TotvsGuiaBackFillController
import br.com.alice.exec.indicator.metrics.registerProcedureExecutedCounter
import br.com.alice.exec.indicator.routes.backFillRoutes
import br.com.alice.exec.indicator.routes.kafkaRoutes
import br.com.alice.exec.indicator.routes.recurringRoutes
import br.com.alice.exec.indicator.service.*
import br.com.alice.exec.indicator.service.internal.*
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.filevault.ioc.FileVaultClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.nullvs.ioc.NullvsIntegrationClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.secondary.attention.ioc.SecondaryAttentionDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.engine.apache.Apache
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    val dependencyInjectionModules = listOf(CommunicationModule,
        MembershipClientModule,
        ClinicalAccountDomainClientModule,
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        StaffDomainClientModule,
        ProviderDomainClientModule,
        PersonDomainClientModule,
        FileVaultClientModule,
        ProductDomainClientModule,
        HealthPlanDomainClientModule,
        EventinderDomainClientModule,
        SecondaryAttentionDomainClientModule,
        NullvsIntegrationClientModule,
        EitaNullvsIntegrationClientModule,
        HealthConditionDomainClientModule,
        AppointmentDomainClientModule,

        module(createdAtStart = true) {
            // config
            single { config }

            // CRM configuration
            single { CrmIdentityProvider(ServiceConfig.Crm.secretKey()) }
            single<CrmAnalyticsTracker> {
                val client = BrazeUserTrackClient(ServiceConfig.Crm.brazeConfig(), Apache.create {
                    customizeClient {
                        setMaxConnTotal(2000)
                        setMaxConnPerRoute(200)
                    }
                })

                BrazeAnalyticsTracker(client, get())
            }

            // Internal
            single { AppointmentProcedureExecutedAliceCodeSourceService(get()) }
            single { AttachmentChemotherapyService(get()) }
            single { AttachmentOpmeService(get()) }
            single { AttachmentRadiotherapyService(get()) }
            single { InternalMvAuthorizedProcedureService(get(), get(), get(), get()) }
            single { InternalExecutionGroupService(get()) }
            single { ExecIndicatorEventService(get()) }
            single { EligibilityCheckService(get()) }
            single { EmailService(get()) }
            single { CreateGuiaItemsService(get(), get(), get(), get()) }
            single { CreateProceduresAndGuiaFromHealthEventsService(get(), get(), get(), get(), get(), get(), get()) }
            single { UpsertGuiaTotvsExtensionService(get(), get(), get(), get(), get()) }
            single { UpsertGuiaTotvsService(get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
            single { UpsertAttachmentOpmeService(get(), get(), get(), get(), get(), get()) }
            single { UpsertAttachmentChemotherapyService(get(), get(), get(), get(), get()) }
            single { UpsertAttachmentRadiotherapyService(get(), get(), get()) }
            single { InternalGuiaService(get(), get(), get()) }
            single { GuiaDetailsService(get(), get(), get(), get(), get(), get(), get()) }
            single { GlossService(get()) }
            single { ResourceBundleSpecialtyService(get()) }
            single { ResourceBundleSpecialtyPricingService(get()) }
            single { ResourceBundleSpecialtyPricingUpdateService(get(), get()) }
            single { ProcessPricingCSVService(get(), get(), get(), get()) }

            // Controllers
            single { HealthController(EXEC_INDICATOR_ROOT_SERVICE_NAME) }

            singleOf(::BackFillController)
            singleOf(::RecurringController)
            single { TotvsGuiaBackFillController(get(), get(), get()) }
            single { GuiaWithProceduresBackfillController(get()) }
            single { GlossAuthorizationInfoController(get()) }
            single { MvAuthorizedProcedureExecutorController(get(), get()) }
            single { HealthSpecialistResourceBundleBackfillController(get(), get()) }

            // Exposed services
            single<HomeService> { HomeServiceImpl(get()) }
            single<CreateGuiaService> { CreateGuiaServiceImpl(get()) }
            single<ExecIndicatorAuthorizerService> { ExecIndicatorAuthorizerServiceImpl(get()) }
            single<ProceduresExecutionService> { ProceduresExecutionServiceImpl(get(), get(), get()) }
            single<ExecutionGroupService> {
                ExecutionGroupServiceImpl(get(), get(), get(), get(), get(), get(), get(), get())
            }
            single<MvAuthorizedProcedureService> { MvAuthorizedProcedureServiceImpl(get(), get()) }
            single<MailerService> { MailerServiceImpl(get()) }
            single<ProcedureProviderService> { ProcedureProviderServiceImpl(get()) }
            single<AuthService> { AuthServiceImpl(get(), get(), get(), get(), get()) }
            single<AuthorizerService> { AuthorizerServiceImpl(get(), get()) }
            single<ExecIndicatorPersonService> {
                ExecIndicatorPersonServiceImpl(
                    get(), get(), get(), get(), get(), get(), get()
                )
            }
            single<AuthorizationService> {
                AuthorizationServiceImpl(get(), get(), get(), get(), get(), get(), get(), get(), get())
            }
            single<ProviderHealthDocumentService> { ProviderHealthDocumentServiceImpl(get()) }
            single<GuiaService> { GuiaServiceImpl(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
            single<EitaHealthEventService> { EitaHealthEventServiceImpl(get(), get()) }
            single<ProfessionalTierProcedureValueService> { ProfessionalTierProcedureValueServiceImpl(get()) }
            single<TussProcedureSpecialtyService> { TussProcedureSpecialtyServiceImpl(get()) }
            single<MagicNumbersService> { MagicNumbersServiceImpl(get(), get()) }
            single<RollbackProceduresExecutionService> { RollbackProceduresExecutionServiceImpl(get(), get()) }
            single<HealthcareResourceGroupService> { HealthcareResourceGroupServiceImpl(get()) }
            single<HealthcareResourceService> { HealthcareResourceServiceImpl(get(), get()) }
            single<HealthcareBundleService> { HealthcareBundleServiceImpl(get(), get(), get(), get()) }
            single<HealthcareResourceGroupAssociationService> {
                HealthcareResourceGroupAssociationServiceImpl(get(), get())
            }
            single<TotvsGuiaService> { TotvsGuiaServiceImpl(get(), get()) }
            single<GlossAuthorizationInfoService> { GlossAuthorizationInfoServiceImpl(get()) }
            single<GuiaWithProceduresService> { GuiaWithProceduresServiceImpl(get()) }
            single<GuiaPrintableService> {
                GuiaPrintableServiceImpl(get(), get(), get(), get(), get(), get(), get(), get(), get())
            }
            single<HospitalizationInfoService> { HospitalizationInfoServiceImpl(get()) }
            single<GuiaAttachmentOpmeService> {
                GuiaAttachmentOpmeServiceImpl(get(), get(), get(), get(), get(), get())
            }
            single<GuiaAttachmentChemotherapyService> {
                GuiaAttachmentChemotherapyServiceImpl(get(), get(), get(), get(), get())
            }
            single<GuiaAttachmentRadiotherapyService> {
                GuiaAttachmentRadiotherapyServiceImpl(get(), get(), get(), get())
            }
            single<GuiaHospitalizationService> { GuiaHospitalizationServiceImpl(get(), get(), get(), get(), get()) }
            single<GuiaAliceHouseService> {
                GuiaAliceHouseServiceImpl(get(), get(), get(), get(), get(), get(), get(), get(), get())
            }
            single<NullvsIntegrationService> { NullvsIntegrationServiceImpl(get(), get()) }
            single<GuiaHospitalHistoryService> { GuiaHospitalHistoryServiceImpl(get(), get()) }
            single<HealthProfessionalOpsProfileService> { HealthProfessionalOpsProfileServiceImpl(get(), get()) }
            single<HealthInstitutionNegotiationService> {
                HealthInstitutionNegotiationServiceImpl(get(), get(), get(), get())
            }
            single<HealthSpecialistResourceBundleService> {
                HealthSpecialistResourceBundleServiceImpl(get(), get(), get())
            }
            single<HealthSpecialistProcedureService> {
                HealthSpecialistProcedureServiceImpl(get(), get(), get())
            }
            single<HealthSpecialistResourceBundleManagementService> {
                HealthSpecialistResourceBundleManagementServiceImpl(get(), get(), get(), get(), get())
            }
            single<ResourceSpecialtyPricingCSVService> {
                ResourceSpecialtyPricingCSVServiceImpl(get(), get(), get(), get(), get(), get(), get())
            }

            // mailer service config
            when (val environment = BaseConfig.instance.runningMode) {
                RunningMode.PRODUCTION -> {
                    single<EmailSenderClient> { PinPointEmailClient(get()) }
                    single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }
                }
                else -> {
                    val awsCredentials = AwsSessionCredentials.create(
                        config.property(
                            "${environment.value.lowercase()}.AWS_ACCESS_KEY_ID"
                        ).getString(), config.property(
                            "${environment.value.lowercase()}.AWS_SECRET_ACCESS_KEY"
                        ).getString(), config.property(
                            "${environment.value.lowercase()}.AWS_SESSION_TOKEN"
                        ).getString()
                    )

                    val awsCredentialsProvider = AwsCredentialsProvider { awsCredentials }

                    val sesClient = SesClient.builder().apply {
                        credentialsProvider(awsCredentialsProvider)
                        region(Region.US_EAST_1)
                    }.build()

                    single<EmailSenderClient> { PinPointEmailClient(get()) }
                    single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(sesClient) }
                }
            }
            single { EmailSender(get(), get()) }

            //Consumers
            single { ExecutionGroupConsumer(get(), get()) }
            single { EligibilityConsumer(get()) }
            single { HealthEventUncoordinatedConsumer(get()) }
            single { ProcessedHealthEventsConsumer(get(), get()) }
            single { NullvsGuiaUpsertedConsumer(get()) }
            single { NullvsGuiaExtensionUpsertedConsumer(get()) }
            single { NullvsAttachmentOpmeUpsertedConsumer(get()) }
            single { NullvsAttachmentChemotherapyUpsertedConsumer(get()) }
            single { NullvsAttachmentRadiotherapyUpsertedConsumer(get()) }
            single { TotvsGuiaUpsertedConsumer(get(), get()) }
            single { HealthcareResourceGroupAssociationUpdatedConsumer(get(), get(), get()) }
            single { ExecuteTotvsGuiaProceduresV2Consumer(get(), get()) }
            single { GuiaWithProceduresUpsertedConsumer(get(), get(), get(), get(), get()) }
            single { UnlinkHealthcareResourceFromHealthcareBundleConsumer(get(), get()) }
            single { HealthcareResourceUpsertedConsumer(get(), get()) }
            single { HealthcareBundleUpsertedConsumer(get(), get()) }
            single { AppointmentProcedureExecutedGroupConsumer(get(), get(), get(), get()) }
            single { MagicNumbersConsumer(get()) }
            single { ResourceBundleSpecialtyPricingUpdateCreatedConsumer(get()) }

            loadServiceServers("br.com.alice.exec.indicator.services")

            // Data services
            single<Invoker> { DataLayerClientConfiguration.build(DefaultHttpClient(timeoutInMillis = 10_000)) }
            single<ExecIndicatorEventModelDataService> { ExecIndicatorEventModelDataServiceClient(get()) }
            single<ExecutionGroupModelDataService> { ExecutionGroupModelDataServiceClient(get()) }
            single<MvAuthorizedProcedureModelDataService> { MvAuthorizedProcedureModelDataServiceClient(get()) }
            single<ExecIndicatorAuthorizerModelDataService> { ExecIndicatorAuthorizerModelDataServiceClient(get()) }
            single<EligibilityCheckModelDataService> { EligibilityCheckModelDataServiceClient(get()) }
            single<ProcedureProviderModelDataService> { ProcedureProviderModelDataServiceClient(get()) }
            single<ProviderHealthDocumentModelDataService> { ProviderHealthDocumentModelDataServiceClient(get()) }
            single<ProfessionalTierProcedureValueModelDataService> { ProfessionalTierProcedureValueModelDataServiceClient(get()) }
            single<TussProcedureSpecialtyModelDataService> { TussProcedureSpecialtyModelDataServiceClient(get()) }
            single<MagicNumbersModelDataService> { MagicNumbersModelDataServiceClient(get()) }
            single<HealthcareResourceGroupModelDataService> { HealthcareResourceGroupModelDataServiceClient(get()) }
            single<HealthcareResourceModelDataService> { HealthcareResourceModelDataServiceClient(get()) }
            single<HealthcareBundleModelDataService> { HealthcareBundleModelDataServiceClient(get()) }
            single<TotvsGuiaModelDataService> { TotvsGuiaModelDataServiceClient(get()) }
            single<HealthcareResourceGroupAssociationModelDataService> {
                HealthcareResourceGroupAssociationModelDataServiceClient(get())
            }
            single<GlossAuthorizationInfoModelDataService> { GlossAuthorizationInfoModelDataServiceClient(get()) }
            single<GuiaWithProceduresModelDataService> { GuiaWithProceduresModelDataServiceClient(get()) }
            single<HospitalizationInfoModelDataService> { HospitalizationInfoModelDataServiceClient(get()) }
            single<AttachmentOpmeModelDataService> { AttachmentOpmeModelDataServiceClient(get()) }
            single<AttachmentChemotherapyModelDataService> { AttachmentChemotherapyModelDataServiceClient(get()) }
            single<AttachmentRadiotherapyModelDataService> { AttachmentRadiotherapyModelDataServiceClient(get()) }
            single<HealthProfessionalOpsProfileModelDataService> { HealthProfessionalOpsProfileModelDataServiceClient(get()) }
            single<HealthInstitutionNegotiationModelDataService> { HealthInstitutionNegotiationModelDataServiceClient(get()) }
            single<HealthSpecialistResourceBundleModelDataService> { HealthSpecialistResourceBundleModelDataServiceClient(get()) }
            single<AppointmentProcedureExecutedAliceCodeSourceModelDataService> {
                AppointmentProcedureExecutedAliceCodeSourceModelDataServiceClient(get())
            }
            single<ResourceBundleSpecialtyModelDataService> { ResourceBundleSpecialtyModelDataServiceClient(get()) }
            single<ResourceBundleSpecialtyPricingModelDataService> { ResourceBundleSpecialtyPricingModelDataServiceClient(get()) }
            single<ResourceBundleSpecialtyPricingUpdateModelDataService> { ResourceBundleSpecialtyPricingUpdateModelDataServiceClient(get()) }
        })
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {

    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        featureConfigBootstrap(
            FeatureNamespace.EXEC_INDICATOR,
            FeatureNamespace.EVENTINDER
        )

        routing {
            application.attributes.put(PolicyRootServiceKey, EXEC_INDICATOR_ROOT_SERVICE_NAME)
            backFillRoutes()
            recurringRoutes()
        }

        kafkaConsumer(startRoutesSync = true) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        registerProcedureExecutedCounter()
    }
}
