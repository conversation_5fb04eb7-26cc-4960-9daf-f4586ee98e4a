package br.com.alice.exec.indicator.routes


import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.exec.indicator.controllers.RecurringController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.recurringRoutes() {
    val recurringController by inject<RecurringController>()

    route("/recurring_subscribers") {
        post("/check_pending_resource_bundle_specialty_pricing_update") { coHandler(recurringController::checkPendingResourceBundleSpecialtyPricingUpdate) }
    }
}
