package br.com.alice.moneyin.services

import br.com.alice.common.PaymentMethod
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheMonth
import br.com.alice.common.core.extensions.atEndOfTheMonth
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapEach
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.services.InvoiceLiquidationModelDataService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import br.com.alice.moneyin.model.InvalidInvoiceLiquidationReasonException
import br.com.alice.moneyin.notification.InvoiceNotifier
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.util.UUID

class InvoiceLiquidationServiceImpl(
    private val invoiceLiquidationDataService: InvoiceLiquidationModelDataService,
    private val invoiceNotifier: InvoiceNotifier,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePaymentService: InvoicePaymentService,
    private val invoicesService: InvoicesService,
) : InvoiceLiquidationService {
    override suspend fun add(invoiceLiquidation: InvoiceLiquidation): Result<InvoiceLiquidation, Throwable> =
        invoiceLiquidationDataService.findOne {
            where {
                externalId.eq(invoiceLiquidation.externalId) and status.diff(
                    InvoiceLiquidationStatus.CANCELED
                )
            }
        }.coFoldNotFound {
            invoiceLiquidationDataService.add(invoiceLiquidation.toModel())
        }.map { it.toTransport() }


    override suspend fun get(id: UUID): Result<InvoiceLiquidation, Throwable> =
        invoiceLiquidationDataService.get(id)
            .map { it.toTransport() }

    override suspend fun update(model: InvoiceLiquidation): Result<InvoiceLiquidation, Throwable> =
        invoiceLiquidationDataService.update(model.toModel())
            .map { it.toTransport() }

    override suspend fun create(payload: InvoiceLiquidationService.CreateInvoiceLiquidationPayload): Result<InvoiceLiquidation, Throwable> {
        val liquidation = InvoiceLiquidation(
            externalId = payload.externalId,
            amount = payload.amount,
            addition = payload.addition,
            discount = payload.discount,
            dueDate = payload.dueDate,
            memberInvoiceGroupIds = payload.memberInvoiceGroupIds,
            billingAccountablePartyId = payload.billingAccountablePartyId,
            companyIds = payload.companyIds,
            subcontractIds = payload.subcontractIds,
            companyId = payload.companyId,
            subcontractId = payload.subcontractId,
            installment = payload.installment,
            totalInstallments = payload.totalInstallments,
            status = InvoiceLiquidationStatus.PROCESSING,
            businessType = payload.businessType,
        )

        if (payload.reason != PaymentReason.B2B_LIQUIDATION && payload.reason != PaymentReason.B2C_LIQUIDATION)
            return InvalidInvoiceLiquidationReasonException("Invalid reason for liquidation").failure()

        return add(liquidation)
            .andThen {
                val paymentMethod =
                    if (payload.reason == PaymentReason.B2C_LIQUIDATION) PaymentMethod.BOLETO else PaymentMethod.BOLEPIX

                invoicePaymentService.createPaymentForLiquidation(
                    invoiceLiquidation = it,
                    paymentMethod = paymentMethod,
                    reason = payload.reason,
                    origin = payload.origin,
                )
            }.flatMap {
                update(it.copy(status = InvoiceLiquidationStatus.PROCESSED))
            }
    }

    override suspend fun cancel(id: UUID): Result<InvoiceLiquidation, Throwable> =
        invoiceLiquidationDataService.get(id)
            .flatMap { invoiceLiquidation ->
                if (invoiceLiquidation.status == InvoiceLiquidationStatus.CANCELED)
                    return@flatMap invoiceLiquidation.success()

                invoiceLiquidationDataService.update(invoiceLiquidation.copy(status = InvoiceLiquidationStatus.CANCELED))
                    .then { logger.info("invoiceLiquidation canceled", "invoiceLiquidation" to invoiceLiquidation) }
                    .andThen {
                        invoicePaymentService.cancelByInvoiceLiquidationId(
                            invoiceLiquidation.id,
                            CancellationReason.CANCELED_BY_LIQUIDATION
                        )
                    }
            }.map { it.toTransport() }

    override suspend fun markAsPaid(
        invoiceLiquidation: InvoiceLiquidation,
        invoicePayment: InvoicePayment
    ): Result<InvoiceLiquidation, Throwable> {
        return invoiceLiquidationDataService.get(invoiceLiquidation.id)
            .flatMap { invoice ->
                logger.info(
                    "InvoicesService::markAsPaid",
                    "id" to invoice.id,
                    "due_date" to invoice.dueDate,
                )
                val updatedInvoice = invoice.copy(
                    status = InvoiceLiquidationStatus.PAID
                )
                invoiceLiquidationDataService.update(updatedInvoice)
                    .then { logger.info("invoiceLiquidation paid", "invoiceLiquidation" to invoice) }
                    .then {
                        invoiceNotifier.publishPaidLiquidationInvoice(
                            updatedInvoice.toTransport(),
                            invoicePayment
                        )
                    }
            }.map { it.toTransport() }
    }


    override suspend fun payMemberInvoiceGroups(invoiceLiquidations: List<InvoiceLiquidation>): Result<Boolean, Throwable> {
        val invoiceLiquidationIds = invoiceLiquidations.map { it.id }
        return memberInvoiceGroupService.getByIds(invoiceLiquidations.first().memberInvoiceGroupIds)
            .then {
                logger.info(
                    "InvoicesService::payMemberInvoiceGroups",
                    "ids" to invoiceLiquidationIds,
                    "member_invoice_group_ids" to invoiceLiquidations.first().memberInvoiceGroupIds,
                )
            }
            .pmapEach {
                memberInvoiceGroupService.markAsPaidByLiquidation(it, invoiceLiquidationIds).get()
            }
            .map { true }
    }


    override suspend fun listByMemberInvoiceGroupIds(ids: List<UUID>) =
        if (ids.isEmpty()) emptyList<InvoiceLiquidation>().success() else
            invoiceLiquidationDataService.find {
                where {
                    memberInvoiceGroupIds.containsAny(ids)
                }
            }.mapEach { it.toTransport() }

    override suspend fun listByExternalIds(ids: List<String>) =
        if (ids.isEmpty()) emptyList<InvoiceLiquidation>().success() else
            invoiceLiquidationDataService.find {
                where {
                    externalId.inList(ids)
                }
            }.mapEach { it.toTransport() }

    override suspend fun listByPersonId(personId: PersonId) =
        invoicesService.listByPersonAndStatuses(personId, listOf(InvoiceStatus.CANCELED_BY_LIQUIDATION))
            .flatMap { memberInvoices ->
                val ids = memberInvoices.mapNotNull { it.memberInvoiceGroupId }

                listByMemberInvoiceGroupIds(ids)
            }

    override suspend fun listByCompanyId(companyId: UUID): Result<List<InvoiceLiquidation>, Throwable> {
        return invoiceLiquidationDataService.find {
            where {
                this.companyIds.contains(companyId)
            }
        }.mapEach { it.toTransport() }
    }

    override suspend fun listByIds(liquidationIds: List<UUID>): Result<List<InvoiceLiquidation>, Throwable> {
        return invoiceLiquidationDataService.find {
            where {
                this.id.inList(liquidationIds)
            }
        }.mapEach { it.toTransport() }
    }

    override suspend fun restoreMemberInvoiceGroups(invoiceLiquidation: InvoiceLiquidation): Result<List<MemberInvoiceGroup>, Throwable> {
        if (invoiceLiquidation.memberInvoiceGroupIds.isEmpty()) return emptyList<MemberInvoiceGroup>().success()

        val memberInvoiceGroups =
            memberInvoiceGroupService.getByIds(invoiceLiquidation.memberInvoiceGroupIds).flatMapEach {
                memberInvoiceGroupService.update(
                    it.copy(
                        invoiceLiquidationIds = emptyList(),
                        status = MemberInvoiceGroupStatus.PROCESSED
                    )
                )
            }.get()

        logger.info("Member invoice groups updated to status PROCESSED", "member_invoice_groups" to memberInvoiceGroups)

        val memberInvoicesIds = memberInvoiceGroups.map { it.memberInvoiceIds }.flatten()
        val memberInvoices = invoicesService.findInvoicesByIds(memberInvoicesIds)
            .pmapEach { invoice -> invoicesService.update(invoice.copy(status = InvoiceStatus.OPEN)) }

        logger.info("Member invoices updated to status OPEN", "member_invoices" to memberInvoices)

        memberInvoiceGroups.forEach { mig ->
            val paymentMethod = if (mig.type?.isB2B() == true) PaymentMethod.BOLEPIX else PaymentMethod.BOLETO
            invoicePaymentService.createInvoicePaymentForInvoiceGroup(
                mig,
                paymentMethod,
                dueDate = LocalDate.now().plusDays(10)
            )
        }

        return memberInvoiceGroups.success()
    }

    override suspend fun getByBillingAccountableAndDueDateAndNotCanceled(
        billingAccountablePartyId: UUID,
        dueDate: LocalDate
    ): Result<List<InvoiceLiquidation>, Throwable> = invoiceLiquidationDataService.find {
        where {
            this.billingAccountablePartyId.eq(billingAccountablePartyId)
                .and(
                    this.dueDate.greaterEq(dueDate.atBeginningOfTheMonth())
                        .and(this.dueDate.lessEq(dueDate.atEndOfTheMonth()))
                        .and(this.status.diff(InvoiceLiquidationStatus.CANCELED))
                )
        }
    }.mapEach { it.toTransport() }

    override suspend fun listInvoicesNearOverdue(
        nearOverdueDate: LocalDate
    ): Result<List<InvoiceLiquidation>, Throwable> =
        invoiceLiquidationDataService.find {
            where {
                status.inList(
                    listOf(
                        InvoiceLiquidationStatus.PROCESSING,
                        InvoiceLiquidationStatus.PROCESSED,
                        InvoiceLiquidationStatus.WAITING_PAYMENT
                    )
                )
                    .and(dueDate.eq(nearOverdueDate))
            }
        }.mapEach { it.toTransport() }

    override suspend fun getBySubContractId(subContractId: UUID): Result<List<InvoiceLiquidation>, Throwable> =
        invoiceLiquidationDataService.findBySubContractId(subContractId).mapEach { it.toTransport() }

    override suspend fun getByBillingAccountablePartyId(billingAccountablePartyId: UUID): Result<List<InvoiceLiquidation>, Throwable> =
        invoiceLiquidationDataService.findByBillingAccountablePartyId(billingAccountablePartyId)
            .pmapEach { it.toTransport() }

}
