package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleUpdateRequest
import br.com.alice.common.core.Status
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthSpecialistResourceBundleInputMapperTest {

    @Test
    fun `#toUpdate returns HealthSpecialistResourceBundle based on HealthSpecialistResourceBundleUpdateRequest`() {
        val model = TestModelFactory.buildHealthSpecialistResourceBundle()

        val request = HealthSpecialistResourceBundleUpdateRequest(
            secondaryResources = emptyList(),
            executionAmount = 1,
            executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
            aliceDescription = "description",
            status = Status.DELETED,
            serviceType = HealthSpecialistResourceBundleServiceType.PROCEDURE,
        )

        val result = HealthSpecialistResourceBundleInputMapper.toUpdate(model, request)

        val expected = model.copy(
            secondaryResources = request.secondaryResources?.map { it.id }?.takeIf { it.isNotEmpty() } ?: emptyList(),
            executionAmount = request.executionAmount,
            executionEnvironment = request.executionEnvironment,
            description = request.aliceDescription,
            status = request.status,
            serviceType = request.serviceType,
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt")
            .isEqualTo(expected)
    }
}
