package br.com.alice.data.layer.models

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

abstract class PaymentDetail {
    abstract val id: UUID
    abstract val paymentId: UUID
    abstract val createdAt: LocalDateTime
    abstract val updatedAt: LocalDateTime

    abstract val method: PaymentMethod
    abstract fun withPaymentId(paymentId: UUID): PaymentDetail
}

data class BoletoPaymentDetail(
    override val paymentId: UUID,
    val dueDate: LocalDateTime,
    @Deprecated("Should use bankSlipBarcodeData or bankSlipDigitableLine")
    val barcode: String? = null,
    @Deprecated("Should use bankSlipBarcodeData or bankSlipDigitableLine")
    val paymentUrl: String? = null,
    val externalId: String? = null,
    val barcodeData: String? = null,
    val digitableLine: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PaymentDetail() {
    override val method get() = PaymentMethod.BOLETO
    override fun withPaymentId(paymentId: UUID) = copy(paymentId = paymentId)
}

data class SimpleCreditCardPaymentDetail(
    override val paymentId: UUID,
    val paymentUrl: String,
    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PaymentDetail() {
    override val method get() = PaymentMethod.SIMPLE_CREDIT_CARD
    override fun withPaymentId(paymentId: UUID) = copy(paymentId = paymentId)
}

data class PixPaymentDetail(
    override val paymentId: UUID,
    @Deprecated("Should use pixQrCode or pixCopyAndPaste")
    val paymentUrl: String? = null,
    val dueDate: LocalDateTime? = null,
    @Deprecated("Should use qrCode or copyAndPaste")
    val paymentCode: String? = null,
    val externalId: String? = null,
    val qrCode: String? = null,
    val copyAndPaste: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PaymentDetail() {
    override val method get() = PaymentMethod.PIX
    override fun withPaymentId(paymentId: UUID) = copy(paymentId = paymentId)
}

data class BolepixPaymentDetail(
    override val paymentId: UUID,
    val paymentUrl: String? = null,
    val dueDate: LocalDateTime? = null,
    @Deprecated("Should use bankSlipBarcodeData or bankSlipDigitableLine")
    val barcodeBoleto: String? = null,
    @Deprecated("Should use pixQrCode or pixCopyAndPaste")
    val paymentCodePix: String? = null,
    @Deprecated("Not needed anymore")
    val boletoPaymentUrl: String? = null,
    @Deprecated("Not needed anymore")
    val pixPaymentUrl: String? = null,
    val externalId: String? = null,
    val pixQrCode: String? = null,
    val pixCopyAndPaste: String? = null,
    val bankSlipBarcodeData: String? = null,
    val bankSlipDigitableLine: String? = null,
    override val id: UUID = RangeUUID.generate(),
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PaymentDetail() {
    override val method get() = PaymentMethod.BOLEPIX
    override fun withPaymentId(paymentId: UUID) = copy(paymentId = paymentId)
}
