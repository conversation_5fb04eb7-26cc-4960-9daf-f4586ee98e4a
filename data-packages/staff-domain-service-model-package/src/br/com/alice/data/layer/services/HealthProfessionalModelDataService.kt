package br.com.alice.data.layer.services

import br.com.alice.common.core.StaffType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsAnyPredicateUsage
import br.com.alice.common.service.data.dsl.Predicate.Companion.ContainsPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.CouncilModel
import br.com.alice.data.layer.models.HealthProfessionalModel
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.services.HealthProfessionalModelDataService.FieldOptions
import br.com.alice.data.layer.services.HealthProfessionalModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthProfessionalModelDataService : Service,
    Adder<HealthProfessionalModel>,
    Counter<FieldOptions, OrderingOptions, HealthProfessionalModel>,
    Finder<FieldOptions, OrderingOptions, HealthProfessionalModel>,
    Getter<HealthProfessionalModel>,
    Updater<HealthProfessionalModel> {
    override val namespace: String
        get() = "staff"
    override val serviceName: String
        get() = "health_professional"

    class Id : Field.UUIDField(HealthProfessionalModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class StaffId : Field.UUIDField(HealthProfessionalModel::staffId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SpecialtyId : Field.UUIDField(HealthProfessionalModel::specialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class SubSpecialtyIds : Field.UUIDField(HealthProfessionalModel::subSpecialtyIds) {
        @OptIn(ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
    }

    class InternalSpecialtyId : Field.UUIDField(HealthProfessionalModel::internalSpecialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class UrlSlug : Field.TextField(HealthProfessionalModel::urlSlug) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class Tier : Field.TextField(HealthProfessionalModel::tier) {
        fun inList(value: List<SpecialistTier>) = Predicate.inList(this, value)
        fun eq(value: SpecialistTier) = Predicate.eq(this, value)
    }

    // WARNING!!!
    // It does not have indexes but it would work because of values low variance of HealthProfessionalAppointmentType.
    // It should not be used alone to not cause a full table scan.
    class AppointmentTypes : Field.TextField(HealthProfessionalModel::appointmentTypes) {
        @OptIn(ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<SpecialistAppointmentType>) = Predicate.containsAny(this, value)
    }

    class ShowOnApp : Field.BooleanField(HealthProfessionalModel::showOnApp)

    class CouncilField : Field.JsonbField(HealthProfessionalModel::council) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: CouncilModel) = Predicate.jsonSearch(this, value.toJson())
    }

    class EmailField : Field.TextField(HealthProfessionalModel::email) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class SearchTokensField : Field.TextField(HealthProfessionalModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class TypeField : Field.TextField(HealthProfessionalModel::type) {
        fun eq(value: StaffType) = Predicate.eq(this, value)
        fun inList(value: List<StaffType>) = Predicate.inList(this, value)
    }

    class StatusField : Field.TextField(HealthProfessionalModel::status) {
        fun eq(value: SpecialistStatus) = Predicate.eq(this, value)
        fun inList(value: List<SpecialistStatus>) = Predicate.inList(this, value)
    }

    class NationalIdField : Field.TextField(HealthProfessionalModel::nationalId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class NameField : Field.TextField(HealthProfessionalModel::name)

    class CreatedAt: Field.DateTimeField(HealthProfessionalModel::createdAt)

    class FieldOptions {
        val id = Id()
        val staffId = StaffId()
        val specialtyId = SpecialtyId()
        val subSpecialtyIds = SubSpecialtyIds()
        val internalSpecialtyId = InternalSpecialtyId()
        val urlSlug = UrlSlug()
        val appointmentTypes = AppointmentTypes()
        val showOnApp = ShowOnApp()
        val tier = Tier()
        val council = CouncilField()
        val email = EmailField()
        val searchTokens = SearchTokensField()
        val type = TypeField()
        val nationalId = NationalIdField()
        val status = StatusField()
    }

    class OrderingOptions {
        val id = Id()
        val staffId = StaffId()
        val createdAt = CreatedAt()
        val name = NameField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: HealthProfessionalModel): Result<HealthProfessionalModel, Throwable>
    override suspend fun update(model: HealthProfessionalModel): Result<HealthProfessionalModel, Throwable>
    override suspend fun get(id: UUID): Result<HealthProfessionalModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthProfessionalModel>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
}
