package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MemberInvoiceType
import br.com.alice.data.layer.models.PreActivationPaymentModel
import br.com.alice.data.layer.models.PreActivationPaymentStatus
import br.com.alice.data.layer.models.PreActivationPaymentType
import com.github.kittinunf.result.Result
import java.time.LocalDate
import java.util.UUID

@RemoteService
interface PreActivationPaymentModelDataService : Service,
    Finder<PreActivationPaymentModelDataService.FieldOptions, PreActivationPaymentModelDataService.OrderingOptions, PreActivationPaymentModel>,
    Getter<PreActivationPaymentModel>,
    Deleter<PreActivationPaymentModel>,
    Updater<PreActivationPaymentModel>,
    Adder<PreActivationPaymentModel>,
    AdderList<PreActivationPaymentModel> {

    override val namespace: String
        get() = "money_in"

    override val serviceName: String
        get() = "pre_activation_payment"

    class Id : Field.UUIDField(PreActivationPaymentModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class MemberInvoiceGroupId : Field.UUIDField(PreActivationPaymentModel::memberInvoiceGroupId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanySubContractId : Field.UUIDField(PreActivationPaymentModel::companySubContractId) {
        fun eq(value: UUID) = Predicate.eq(this, value)

        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CompanyId : Field.UUIDField(PreActivationPaymentModel::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonId : Field.TableIdField(PreActivationPaymentModel::personId) {
        fun inList(value: List<br.com.alice.common.core.PersonId>) = Predicate.inList(this, value)
    }

    class ReferenceDate : Field.DateField(PreActivationPaymentModel::referenceDate) {
        fun eq(value: LocalDate) = Predicate.eq(this, value)
        fun lessEq(value: LocalDate) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDate) = Predicate.greaterEq(this, value)
        fun inList(value: List<LocalDate>) = Predicate.inList(this, value)
    }

    class Status : Field.TextField(PreActivationPaymentModel::status) {
        fun eq(value: PreActivationPaymentStatus) = Predicate.eq(this, value)
        fun inList(value: List<PreActivationPaymentStatus>) = Predicate.inList(this, value)
    }

    class Type : Field.TextField(PreActivationPaymentModel::type) {
        fun eq(value: PreActivationPaymentType) = Predicate.eq(this, value)
        fun inList(value: List<PreActivationPaymentType>) = Predicate.inList(this, value)
    }

    class BillingAccountablePartyId : Field.UUIDField(PreActivationPaymentModel::billingAccountablePartyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = Id()
        val personId = PersonId()
        val memberInvoiceGroupId = MemberInvoiceGroupId()
        val companySubContractId = CompanySubContractId()
        val companyId = CompanyId()
        val referenceDate = ReferenceDate()
        val status = Status()
        val type = Type()
        val billingAccountablePartyId = BillingAccountablePartyId()
    }

    class OrderingOptions {
        val referenceDate = ReferenceDate()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<PreActivationPaymentModel>, Throwable>

    override suspend fun add(model: PreActivationPaymentModel): Result<PreActivationPaymentModel, Throwable>

    override suspend fun addList(models: List<PreActivationPaymentModel>): Result<List<PreActivationPaymentModel>, Throwable>

    override suspend fun get(id: UUID): Result<PreActivationPaymentModel, Throwable>

    override suspend fun update(model: PreActivationPaymentModel): Result<PreActivationPaymentModel, Throwable>

    override suspend fun delete(model: PreActivationPaymentModel): Result<Boolean, Throwable>

    suspend fun findByBillingAccountablePartyId(billingAccountablePartyId: UUID) = find {
        where {
            this.billingAccountablePartyId.eq(billingAccountablePartyId)
        }
    }
}
