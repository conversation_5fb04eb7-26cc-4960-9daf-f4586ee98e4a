package br.com.alice.common.observability.opentelemetry

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.logging.Logger
import br.com.alice.common.observability.opentelemetry.CommonServerAttributes.gitHash
import br.com.alice.common.observability.opentelemetry.CommonServerAttributes.serviceName
import br.com.alice.common.observability.opentelemetry.OpenTelemetryServer.Configuration.Companion.defaultFilter
import br.com.alice.common.observability.opentelemetry.exporter.LoggerExporter
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.request.path
import io.opentelemetry.api.trace.propagation.W3CTraceContextPropagator
import io.opentelemetry.context.propagation.ContextPropagators
import io.opentelemetry.exporter.jaeger.JaegerGrpcSpanExporter
import io.opentelemetry.sdk.OpenTelemetrySdk
import io.opentelemetry.sdk.resources.Resource
import io.opentelemetry.sdk.trace.SdkTracerProvider
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor
import io.opentelemetry.sdk.trace.export.SpanExporter
import io.opentelemetry.semconv.resource.attributes.ResourceAttributes.SERVICE_NAME
import java.util.concurrent.TimeUnit


private fun initTracing() {
    val multiSpanExporter = SpanExporter.composite(emptyList())
    val spanProcessor = BatchSpanProcessor.builder(multiSpanExporter).setScheduleDelay(100L, TimeUnit.MILLISECONDS).build()

    val resource = Resource.getDefault()
        .toBuilder()
        .put(SERVICE_NAME, serviceName)
        .build()

    val tracerProvider = SdkTracerProvider.builder()
        .addSpanProcessor(spanProcessor)
        .setResource(resource)
        .build()

    OpenTelemetrySdk.builder()
        .setTracerProvider(tracerProvider)
        .setPropagators(ContextPropagators.create(W3CTraceContextPropagator.getInstance()))
        .buildAndRegisterGlobal()
}

fun Application.setupOpenTelemetry() {
    try {
        if (BaseConfig.instance.runningMode != RunningMode.TEST) {
            val serverAttributes = mapOf(
                "service_name" to serviceName,
                "git_hash" to gitHash
            )

            initTracing()

            install(OpenTelemetryServer) {
                this.serverAttributes = serverAttributes
                filter = { defaultFilter(it) && !it.request.path().contains("/mv_integration_finished/") }
            }

            Logger.info(
                "Setup OpenTelemetry done",
                "setup" to true,
            )
        }
    } catch (ex: Exception) {
        Logger.error("Setup OpenTelemetry failure", ex)
    }
}
