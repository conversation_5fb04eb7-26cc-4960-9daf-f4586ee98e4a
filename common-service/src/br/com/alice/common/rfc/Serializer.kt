package br.com.alice.common.rfc

import br.com.alice.common.core.exceptions.GatewayTimeoutException
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.RfcException
import br.com.alice.common.core.extensions.classQualifiedName
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.opentelemetry.Tracer
import br.com.alice.common.observability.recordResult
import br.com.alice.common.service.serialization.registerDurationAdapter
import br.com.alice.common.service.serialization.registerLocalDateAdapter
import br.com.alice.common.service.serialization.registerLocalDateTimeAdapter
import br.com.alice.common.service.serialization.registerLocalTimeAdapter
import br.com.alice.common.service.serialization.registerTimestampDeserializerAdapter
import br.com.alice.common.service.serialization.registerTimestampTimeSerializerAdapter
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import com.google.gson.JsonParser
import com.google.gson.reflect.TypeToken
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.opentelemetry.api.trace.SpanKind
import kotlin.reflect.full.primaryConstructor

val rfcGson: Gson = GsonBuilder()
    .registerLocalDateTimeAdapter()
    .registerLocalDateAdapter()
    .registerLocalTimeAdapter()
    .registerTimestampDeserializerAdapter()
    .registerTimestampTimeSerializerAdapter()
    .registerDurationAdapter()
    .create()

object Serializer {

    val ignoreClasses = listOf("kotlin.collections.List")

    inline fun <reified T> encode(value: T): String = rfcGson.toJson(value)

    fun <V : Any, E : Throwable> encodeResult(result: Result<V, E>): String =
        result.fold(
            { success ->
                encode(
                    mapOf(
                        "success" to success,
                        "classQualifiedName" to success.classQualifiedName()
                    )
                )
            },
            { failure ->
                encode(mapOf("error" to SerializerRfcException.encode(failure)))
            }
        )

    inline fun <reified T> decode(json: JsonElement): T = rfcGson.fromJson(json, object: TypeToken<T>() {}.type)

    inline fun <reified T> decode(str: String): T = rfcGson.fromJson(str)

    inline fun <reified T : Any> decodeResult(input: String): Result<T, Throwable> =
        Tracer.spanSync("Serializer.decodeResult", SpanKind.INTERNAL, "DEBUG") { span ->
            val jsonElement: JsonElement = JsonParser.parseString(input)
            val value = jsonElement.asJsonObject["success"]

            resultOf<T, Throwable> {
                value?.let {
                    val expectedClass = T::class.qualifiedName
                    val actualClass = jsonElement.asJsonObject["classQualifiedName"]?.asString

                    val type =
                        if (actualClass == null || actualClass == expectedClass || expectedClass in ignoreClasses) object :
                            TypeToken<T>() {}.type else Class.forName(actualClass)

                    (rfcGson.fromJson(value, type) as T)
                } ?: throw SerializerRfcException.decode(jsonElement.asJsonObject["error"])
            }.recordResult(span)
        }

}

object SerializerRfcException {

    fun encode(exception: Throwable): Map<String, Any?> =
        when (exception) {
            is RfcException -> exception
            is HttpRequestTimeoutException -> GatewayTimeoutException(
                message = exception.message ?: "RFC timeout exception",
                cause = exception
            )
            is JsonParseException,
            is IllegalArgumentException -> InvalidArgumentException(
                message = exception.message ?: "Invalid Argument",
                cause = exception
            )
            else -> {
                logger.error("The exception is not an RfcException: ${exception.classSimpleName()}")
                InternalServiceErrorException(
                    message = exception.message ?: "Invalid RFC exception",
                    cause = exception
                )
            }
        }.let { rfcException ->
            mapOf(
                "type" to rfcException::class.qualifiedName!!,
                "exception" to rfcException.encode()
            )
        }

    fun decode(jsonElement: JsonElement): Exception = decodeRfcException(jsonElement)

    private fun decodeRfcException(jsonElement: JsonElement): RfcException {
        val type = jsonElement.asJsonObject["type"].asString
        val jsonException = jsonElement.asJsonObject["exception"].asJsonObject
        val stackTrace = jsonException["stackTrace"]?.asJsonArray?.asList() ?: emptyList()

        val constructor = Class.forName(type) .kotlin.primaryConstructor!!

        val parameters = constructor.parameters.associateWith { parameter ->
            val value = jsonException[parameter.name].getOrNull()

            if (parameter.type.classifier == Throwable::class) value?.let { Exception(it) }
            // make backwards compatibility until migration
            else if (parameter.name == RfcException::message.name && value == null) jsonException["detailMessage"].getOrNull()
            else value
        }

        return (constructor.callBy(parameters) as RfcException).also { exception ->
            exception.stackTrace = stackTrace.map { it.toStackTraceElement() }.toTypedArray()
        }
    }

    private fun RfcException.encode() =
        mapOf(
            "message" to message,
            "code" to code,
            "stackTrace" to stackTrace.toList().take(5).map { it.toMap() }.let { rfcGson.toJsonTree(it) }
        )

    private fun StackTraceElement.toMap() =
        mapOf(
            "className" to className,
            "methodName" to methodName,
            "fileName" to fileName,
            "lineNumber" to lineNumber
        )

    private fun JsonElement.toStackTraceElement() =
        asJsonObject.let { jsonObject ->
            StackTraceElement(
                jsonObject["className"]?.asString,
                jsonObject["methodName"]?.asString,
                jsonObject["fileName"]?.asString,
                jsonObject["lineNumber"]?.asInt ?: -1
            )
        }

    private fun JsonElement?.getOrNull() =
        this?.let { if (it.isJsonNull) null else it.asString }
}
