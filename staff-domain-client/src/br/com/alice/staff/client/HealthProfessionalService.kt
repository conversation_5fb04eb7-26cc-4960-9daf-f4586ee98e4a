package br.com.alice.staff.client

import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.SpecialistStatus.ACTIVE
import br.com.alice.data.layer.models.SpecialtyTiers
import br.com.alice.staff.models.StaffWithHealthProfessional
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HealthProfessionalService : Service {
    override val namespace: String
        get() = "staff"
    override val serviceName: String
        get() = "health_professional"

    suspend fun get(
        id: UUID,
        findOptions: FindOptions = FindOptions(withStaff = false, withContact = false),
    ): Result<HealthProfessional, Throwable>

    suspend fun getByIds(
        ids: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun getByRoleAndRange(
        role: Role,
        range: IntRange
    ): Result<List<HealthProfessional>, Throwable>

    /**
     * Finds health professionals by their staff IDs with staff included.
     */
    suspend fun getByStaffIds(
        staffIds: List<UUID>
    ): Result<List<HealthProfessional>, Throwable>

    /**
     * Finds health professionals by their staff IDs without staff included.
     */
    suspend fun findByStaffIds(
        staffIds: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun findByStaffId(
        staffId: UUID,
        findOptions: FindOptions = FindOptions(),
    ): Result<HealthProfessional, Throwable>

    suspend fun getByUrlSlugAndRoles(
        urlSlug: String,
        roles: List<Role>,
    ): Result<HealthProfessional, Throwable>

    suspend fun getByUrlSlug(
        urlSlug: String
    ): Result<HealthProfessional, Throwable>

    suspend fun searchByNameAndRoleWithRange(
        staffIds: List<UUID>? = null,
        range: IntRange,
        roles: List<Role>,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>? = null,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun countByNameAndRoleWithRange(
        staffIds: List<UUID>?,
        roles: List<Role>,
        namePrefix: String?,
        active: Boolean?,
        types: List<StaffType>? = null,
    ): Result<Int, Throwable>

    suspend fun findBySpecialtyIds(
        specialtyIds: List<UUID>
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun getByIdsAndSpecialtyIds(
        ids: List<UUID>,
        specialtyIds: List<UUID>,
    ): Result<List<StaffWithHealthProfessional>, Throwable>

    suspend fun findBySpecialtyIdsAndRole(
        specialtyIds: List<UUID>,
        role: Role
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun findBySubSpecialtyId(
        subSpecialtyId: UUID
    ): Result<HealthProfessional, Throwable>

    suspend fun findByInternalSpecialties(
        internalSpecialtyIds: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun findActivesByInternalAndExternalSpecialties(
        specialtyIds: List<UUID>,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun findActivesWithAnyRole(
        roles: List<Role>,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun setOnCall(
        staffId: UUID,
        onCall: Boolean
    ): Result<HealthProfessional, Throwable>

    suspend fun findStaffsWithHealthProfessionalIfExists(
        staffIds: List<UUID>,
    ): Result<List<StaffWithHealthProfessional>, Throwable>

    suspend fun searchByAppointmentTypeSpecialtyTiersAndStaffIds(
        showOnApp: Boolean?,
        appointmentTypes: List<SpecialistAppointmentType>? = emptyList(),
        specialtyTiers: List<SpecialtyTiers>,
        staffIds: List<UUID>,
        range: IntRange,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun countByAppointmentTypeSpecialtyTiersAndStaffIds(
        showOnApp: Boolean?,
        appointmentTypes: List<SpecialistAppointmentType>?,
        specialtyTiers: List<SpecialtyTiers>,
        staffIds: List<UUID>,
    ): Result<Int, Throwable>

    suspend fun countBySpecialtyIdAndTier(
        specialty: UUID,
        tier: SpecialistTier
    ): Result<Int, Throwable>

    suspend fun countBySpecialtyIdsAndTiers(
        specialtyIdList: List<UUID>,
        tiersList: List<SpecialistTier>
    ): Result<Int, Throwable>

    suspend fun getBySpecialtyIdsAndTiers(
        specialtyIdList: List<UUID>,
        tiersList: List<SpecialistTier>
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun existsByCouncil(
        council: Council
    ): Result<Boolean, Throwable>

    suspend fun inactivateById(
        id: UUID
    ): Result<Boolean, Throwable>

    suspend fun changeShowOnApp(id: UUID, showOnApp: Boolean): Result<HealthProfessional, Throwable>

    suspend fun getByRange(
        range: IntRange,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun getSpecialistsByRange(range: IntRange): Result<List<HealthProfessional>, Throwable>

    suspend fun findByEmail(email: String, active: SpecialistStatus = ACTIVE): Result<HealthProfessional, Throwable>

    suspend fun updateVacation(
        id: UUID,
        startDate: LocalDateTime?,
        endDate: LocalDateTime?
    ): Result<HealthProfessional, Throwable>

    suspend fun searchByNameAndIdWithRange(
        range: IntRange,
        namePrefix: String? = null,
        ids: List<UUID>? = null
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun countByNameAndId(
        namePrefix: String? = null,
        ids: List<UUID>? = null
    ): Result<Int, Throwable>

    suspend fun getByFilterAndRange(
        namePrefix: String,
        statusFilter: SpecialistStatus,
        range: IntRange,
    ): Result<List<HealthProfessional>, Throwable>

    suspend fun countByFilter(
        namePrefix: String,
        statusFilter: SpecialistStatus,
    ): Result<Int, Throwable>

    suspend fun countAllSpecialists(): Result<Int, Throwable>

    data class FindOptions(
        val withStaff: Boolean = false,
        val withContact: Boolean = false,
        val status: List<SpecialistStatus>? = null,
    )
}
