package br.com.alice.duquesa.clients.cia_consulta

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleCancelRequest
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleCancelResponse
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleConfirmRequest
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleConfirmResponse
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleRequest
import br.com.alice.duquesa.clients.cia_consulta.models.ScheduleResponse
import br.com.alice.duquesa.clients.cia_consulta.models.Slot
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import io.ktor.http.content.TextContent
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.net.URLDecoder
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.Ignore
import kotlin.test.Test


class CiaScheduleClientTest : ProxxiaCiaBaseTest() {

    private val requestSchedule = ScheduleRequest(
        slotId = 10265103,
        personId = idPersonCia,
        productId = **********,
        medicalInsuranceId = 15,
        medicalInsuranceCategoryId = 1,
        sellerId = null
    )
    private val confirmRequest = ScheduleConfirmRequest(
        slotId = requestSchedule.slotId
    )
    private val cancelRequest = ScheduleCancelRequest(
        slotId = requestSchedule.slotId
    )

    @Test
    @Ignore("Not run by default because call Cia test server")
    fun `#registerSchedule should register schedule in cia`() = runBlocking {
        val authClient = CiaAuthClient(DefaultHttpClient(timeoutInMillis = 10_000), cache)
        val ciaClient = CiaScheduleClient(DefaultHttpClient(timeoutInMillis = 50_000))
        val token = authClient.auth().get()

        val response = ciaClient.registerSchedule(requestSchedule, token)

        ResultAssert.assertThat(response).isSuccessOfType(ScheduleResponse::class)
    }

    @Test
    @Ignore("Not run by default because call Cia test server")
    fun `#confirmSchedule should confirm schedule in cia`() = runBlocking {
        val authClient = CiaAuthClient(DefaultHttpClient(timeoutInMillis = 10_000), cache)
        val ciaClient = CiaScheduleClient(DefaultHttpClient(timeoutInMillis = 50_000))
        val token = authClient.auth().get()

        val response = ciaClient.confirmSchedule(confirmRequest, token)

        ResultAssert.assertThat(response).isSuccessOfType(ScheduleConfirmResponse::class)
    }

    @Test
    @Ignore("Not run by default because call Cia test server")
    fun `#cancelSchedule should cancel schedule in cia`() = runBlocking {

        val authClient = CiaAuthClient(DefaultHttpClient(timeoutInMillis = 10_000), cache)
        val ciaClient = CiaScheduleClient(DefaultHttpClient(timeoutInMillis = 50_000))
        val token = authClient.auth().get()

        val response = ciaClient.cancelSchedule(cancelRequest, token)

        ResultAssert.assertThat(response).isSuccessOfType(ScheduleCancelResponse::class)
    }

    @Test
    fun `#registerSchedule should register schedule in cia mock`() = runBlocking {
        val expected = ScheduleResponse(
            Slot(
                id = requestSchedule.slotId,
                status = "available",
                scheduleDate = LocalDateTime.parse(
                    "2023-04-12 16:40:00",
                    DateTimeFormatter.ofPattern(DATE_FORMAT)
                ),
                duration = 20,
                professionalId = 21263,
                establishmentId = 10,
                availableProductIds = listOf(**********),
                slotType = "consult",
                minAge = 16,
                maxAge = 150,
                productId = 101700,
                medicalInsuranceId = 1,
                medicalInsuranceCategoryId = 1,
                personId = requestSchedule.personId,
                roomId = null
            ),
            success = true
        )
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/api/schedules")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)
                    Assertions.assertThat(request.headers["Authorization"]).isEqualTo(token.token)

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    Assertions.assertThat(textContent.text)
                        .isEqualTo(gson.toJson(requestSchedule))

                    respond(expectedJson)
                }
            }
        }

        val clientMock = CiaScheduleClient(httpClientMock)

        val response = clientMock.registerSchedule(requestSchedule, token)
        ResultAssert.assertThat(response).isSuccessWithData(expected)
    }

    @Test
    fun `#confirmSchedule should register schedule in cia mock`() = runBlocking {
        val expected = ScheduleConfirmResponse(
            confirm = true,
            success = true
        )
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/api/schedules/confirm")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)
                    Assertions.assertThat(request.headers["Authorization"]).isEqualTo(token.token)

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    Assertions.assertThat(textContent.text)
                        .isEqualTo(gson.toJson(confirmRequest))

                    respond(expectedJson)
                }
            }
        }

        val clientMock = CiaScheduleClient(httpClientMock)

        val response = clientMock.confirmSchedule(confirmRequest, token)
        ResultAssert.assertThat(response).isSuccessWithData(expected)
    }

    @Test
    fun `#cancelSchedule should register schedule in cia mock`() = runBlocking {
        val expected = ScheduleCancelResponse(
            confirm = true,
            success = true
        )
        val expectedJson = gson.toJson(expected)

        val httpClientMock = HttpClient(MockEngine) {
            engine {
                addHandler { request ->
                    Assertions.assertThat(URLDecoder.decode(request.url.toString(), "utf-8"))
                        .isEqualTo("${credentials.apiUrl}/api/schedules/cancel")
                    Assertions.assertThat(request.method).isEqualTo(HttpMethod.Post)
                    Assertions.assertThat(request.headers["Authorization"]).isEqualTo(token.token)

                    val textContent = (request.body as TextContent)
                    Assertions.assertThat(textContent.contentType)
                        .isEqualTo(ContentType.Application.Json)
                    Assertions.assertThat(textContent.text)
                        .isEqualTo(gson.toJson(cancelRequest))

                    respond(expectedJson)
                }
            }
        }

        val clientMock = CiaScheduleClient(httpClientMock)

        val response = clientMock.cancelSchedule(cancelRequest, token)
        ResultAssert.assertThat(response).isSuccessWithData(expected)
    }

}
