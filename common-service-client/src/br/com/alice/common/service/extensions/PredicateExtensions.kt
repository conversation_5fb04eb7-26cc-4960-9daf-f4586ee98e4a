package br.com.alice.common.service.extensions

import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and

@RequiresOptIn(message = "Avoid use with complex predicates like and/or because the internal AND predicate usage can create a stack of predicates that can introduce weird behaviours, such as breaking your tests. Use for simple filter chain.")
@Retention(AnnotationRetention.BINARY)
@Target(AnnotationTarget.CLASS, AnnotationTarget.FUNCTION)
annotation class WithFilterPredicateUsage

/**
 * How to use:
 * 1 - use this function inside `where` starting with a fixed predicate e.g:
 *
 *      where {
 *          this.field1.eq(x)
 *              .withFilter(y) { validY -> this.field2.eq(validY) }!!
 *      }
 *
 *
 * 2 - use this function inside `where` starting with no fixed predicate () e.g:
 * <bold>Make sure that at least one of the fields exists, as where requires a predicate</bold>
 *
 *     where {
 *          basePredicateForFilters()
 *              .withFilter(x) { validX -> this.field1.eq(validX) }
 *              .withFilter(y) { validY -> this.field2.eq(validY) }!!
 *     }
 *
 *
 * @param value T, a value to use in the predicate
 * @param function (T) -> Predicate, a function to create a new or concatenate predicate with value
 * @return Predicate?, can be a new predicate, null or concatenate predicate with `and`
 */
@WithFilterPredicateUsage
fun <T> Predicate?.withFilter(
    value: T?,
    function: (T) -> Predicate
): Predicate? =
    if (!value.canConcat()) this
    else if (this == null) function.invoke(value!!)
    else this and function.invoke(value!!)

private fun Any?.canConcat() = when (this) {
    null -> false
    is String -> this.isNotBlank()
    is Collection<*> -> this.filterNotNull().isNotEmpty()
    else -> true
}

fun basePredicateForFilters() : Predicate? = null
