package br.com.alice.healthplan.models

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TestRequest
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class TestRequestTest {

    @Test
    fun `#formattedTitle generates title with title and description`() {
        val testRequest: TestRequest = TestModelFactory.buildHealthPlanTaskTestRequest().specialize()
        assertThat(testRequest.formattedTitle()).isEqualTo("some title - some description")
    }

    @Test
    fun `#formattedTitle generates title with null description`() {
        val testRequest: TestRequest = TestModelFactory.buildHealthPlanTaskTestRequest(description = null).specialize()
        assertThat(testRequest.formattedTitle()).isEqualTo("some title")
    }

    @Test
    fun `#formattedTitle generates title with blank description`() {
        val testRequest: TestRequest = TestModelFactory.buildHealthPlanTaskTestRequest(description = " ").specialize()
        assertThat(testRequest.formattedTitle()).isEqualTo("some title")
    }
}
