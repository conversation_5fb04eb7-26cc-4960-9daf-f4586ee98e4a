plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.itau-integration-client"
version = aliceItauIntegrationClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
    implementation(project(":test-result-domain-client"))
    implementation(project(":money-in-domain-client"))
    implementation(project(":data-packages:money-in-domain-service-data-package"))
    kapt(project(":common"))
    ktor2Dependencies()
    api("com.github.kittinunf.result:result:$resultVersion")

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
